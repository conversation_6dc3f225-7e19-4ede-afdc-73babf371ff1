export default {
  'smart.quality.evaluation.table.title': 'Formulir penilaian kualitas cerdas',
  'smart.quality.evaluation.table.add': '<PERSON>bah formulir penilaian',
  'smart.quality.evaluation.table.name': '<PERSON><PERSON> formulir penilaian',
  'smart.quality.evaluation.rule.category': '<PERSON><PERSON><PERSON> aturan',
  'smart.quality.evaluation.assessment.name': '<PERSON><PERSON> formulir penilaian',
  'smart.quality.evaluation.table.channel': 'Saluran',
  'smart.quality.evaluation.table.channel.all': '<PERSON><PERSON><PERSON> saluran',
  'smart.quality.evaluation.table.ticket.type': '<PERSON><PERSON> tike<PERSON>',
  'smart.quality.evaluation.table.status': 'Status publikasi',
  'smart.quality.evaluation.table.status.published': 'Dipublikasikan',
  'smart.quality.evaluation.table.status.disabled': 'Tidak dipublikasikan',
  'smart.quality.evaluation.table.rule.count': '<PERSON><PERSON><PERSON> aturan penilaian',
  'smart.quality.evaluation.table.total.score': 'Total skor',
  'smart.quality.evaluation.table.operation': '<PERSON><PERSON><PERSON>',
  'smart.quality.evaluation.table.enable': 'Aktif<PERSON>',
  'smart.quality.evaluation.table.enable.success': '<PERSON>r<PERSON><PERSON> diaktifkan',
  'smart.quality.evaluation.table.disable': 'Nonaktifkan',
  'smart.quality.evaluation.table.disable.success': 'Berhasil dinonaktifkan',
  'smart.quality.evaluation.table.edit': 'Edit',
  'smart.quality.evaluation.table.edit.rule': 'Edit aturan',
  'smart.quality.evaluation.table.history': 'Riwayat evaluasi',
  'smart.quality.evaluation.table.delete': 'Hapus',
  'smart.quality.evaluation.table.delete.confirm':
    'Hapus formulir penilaian ini?',
  'smart.quality.evaluation.table.delete.ok': 'Ya',
  'smart.quality.evaluation.table.delete.cancel': 'Tidak',
  'smart.quality.evaluation.table.delete.success': 'Berhasil dihapus',
  'smart.quality.evaluation.list.page.total.num': 'Total {total} item',
  // ====== 添加评估表页面相关 ======
  'smart.quality.evaluation.add': 'Tambah formulir penilaian',
  'smart.quality.evaluation.add.baseinfo': 'Informasi dasar',
  'smart.quality.evaluation.add.rule': 'Aturan penilaian',
  'smart.quality.evaluation.add.permission': 'Pengaturan izin',
  'smart.quality.evaluation.add.name': 'Nama formulir penilaian',
  'smart.quality.evaluation.add.name.placeholder':
    'Masukkan nama formulir penilaian',
  'smart.quality.evaluation.add.name.required':
    'Masukkan nama formulir penilaian',
  'smart.quality.evaluation.add.name.max':
    'Panjang tidak boleh melebihi 80 karakter',
  'smart.quality.evaluation.add.channel': 'Saluran yang berlaku',
  'smart.quality.evaluation.add.channel.placeholder':
    'Pilih saluran yang berlaku',
  'smart.quality.evaluation.add.channel.required': 'Pilih saluran yang berlaku',
  'smart.quality.evaluation.add.ticket.type': 'Jenis tiket yang berlaku',
  'smart.quality.evaluation.add.ticket.type.placeholder':
    'Pilih jenis tiket yang berlaku',
  'smart.quality.evaluation.add.ticket.type.required':
    'Pilih jenis tiket yang berlaku',
  'smart.quality.evaluation.add.total.score': 'Skor penuh',
  'smart.quality.evaluation.add.total.score.placeholder': 'Masukkan skor penuh',
  'smart.quality.evaluation.add.total.score.required': 'Masukkan skor penuh',
  'smart.quality.evaluation.add.score.mechanism': 'Mekanisme penilaian',
  'smart.quality.evaluation.add.score.mechanism.add': 'Penilaian aditif',
  'smart.quality.evaluation.add.score.mechanism.subtract': 'Penilaian deduktif',
  'smart.quality.evaluation.add.score.mechanism.tooltip.add.title':
    'Penilaian aditif',
  'smart.quality.evaluation.add.score.mechanism.tooltip.add.1':
    'Penilaian nilai tambah kinerja',
  'smart.quality.evaluation.add.score.mechanism.tooltip.add.2':
    'Dimulai dari skor dasar, menambah poin untuk kinerja yang sangat baik',
  'smart.quality.evaluation.add.score.mechanism.tooltip.add.3':
    'Menekankan perilaku positif dan nilai tambah di luar standar',
  'smart.quality.evaluation.add.score.mechanism.tooltip.subtract.title':
    'Penilaian deduktif',
  'smart.quality.evaluation.add.score.mechanism.tooltip.subtract.1':
    'Penilaian dasar kepatuhan',
  'smart.quality.evaluation.add.score.mechanism.tooltip.subtract.2':
    'Dimulai dari skor penuh, mengurangi poin untuk item yang tidak sesuai',
  'smart.quality.evaluation.add.score.mechanism.tooltip.subtract.3':
    'Menekankan pemeliharaan standar layanan dasar dan kepatuhan',
  'smart.quality.evaluation.add.score.mechanism.required':
    'Pilih mekanisme penilaian',
  'smart.quality.evaluation.add.permission.scorer': 'Penilai',
  'smart.quality.evaluation.add.permission.scorer.tooltip':
    'Menentukan siapa yang dapat memberikan skor berdasarkan formulir penilaian ini',
  'smart.quality.evaluation.add.permission.scorer.required': 'Pilih penilai',
  'smart.quality.evaluation.add.permission.scorer.placeholder': 'Pilih penilai',
  'smart.quality.evaluation.add.cancel.confirm':
    'Membatalkan akan menghapus formulir. Apakah Anda yakin ingin membatalkan?',
  'smart.quality.common.cancel': 'Batal',
  'smart.quality.common.next': 'Selanjutnya',
  'smart.quality.common.yes': 'Ya',
  'smart.quality.common.no': 'Tidak',
  'smart.quality.evaluation.add.channel.all': 'Semua',
  'smart.quality.evaluation.add.ticket.type.all': 'Semua',
  // ====== END 添加评估表页面相关 ======
  // ====== 评估历史记录页面相关 ======
  'smart.quality.evaluation.history.title': 'Riwayat evaluasi',
  'smart.quality.evaluation.history.name.placeholder':
    'Masukkan nama formulir penilaian',
  'smart.quality.evaluation.history.channel.placeholder': 'Pilih saluran',
  'smart.quality.evaluation.history.ticket.type.placeholder':
    'Pilih jenis tiket',
  'smart.quality.evaluation.history.agent.name': 'Nama agen',
  'smart.quality.evaluation.history.agent.name.placeholder': 'Pilih nama agen',
  'smart.quality.evaluation.history.ticket.id': 'ID Tiket',
  'smart.quality.evaluation.history.ticket.id.placeholder': 'Masukkan ID tiket',
  'smart.quality.evaluation.history.score': 'Skor',
  'smart.quality.evaluation.history.evaluator': 'Penilai',
  'smart.quality.evaluation.history.evaluator.placeholder': 'Pilih penilai',
  'smart.quality.evaluation.history.score.range': 'Rentang skor',
  'smart.quality.evaluation.history.score.min': 'Min',
  'smart.quality.evaluation.history.score.max': 'Maks',
  'smart.quality.evaluation.history.score.min.error':
    'Nilai min tidak boleh lebih besar dari nilai maks',
  'smart.quality.evaluation.history.score.max.error':
    'Nilai maks tidak boleh lebih kecil dari nilai min',
  'smart.quality.evaluation.history.score.range.error':
    'Nilai maks tidak boleh lebih kecil dari nilai min',
  'smart.quality.evaluation.history.score.both.required':
    'Nilai maks dan min keduanya diperlukan',
  'smart.quality.evaluation.history.score.range.warning':
    'Nilai min tidak boleh lebih besar dari nilai maks',
  'smart.quality.evaluation.history.score.format.error':
    'Silakan masukkan angka yang valid dengan maksimal 2 tempat desimal',
  'smart.quality.evaluation.history.search': 'Cari',
  'smart.quality.evaluation.history.details': 'Detail',
  'smart.quality.evaluation.history.export.pdf': 'Ekspor PDF',
  'smart.quality.evaluation.history.page.total.num': 'Total {total} item',
  'smart.quality.evaluation.history.evaluation.time': 'Waktu evaluasi',
  'smart.quality.evaluation.history.return.list': 'Kembali ke daftar',
  // ====== END 评估历史记录页面相关 ======
  'smart.quality.evaluation.rule.version.current': 'Versi terbaru',
  'smart.quality.evaluation.rule.version.current.tip': 'Pilih versi terbaru',
  'smart.quality.evaluation.rule.delete.category.tip.title': 'Peringatan',
  'smart.quality.evaluation.rule.delete.category.tip':
    'Kategori ini berisi aturan. Menghapusnya juga akan menghapus semua aturan di dalamnya',
  // start 规则列表
  'smart.quality.evaluation.rule.category': 'Kategori aturan',
  'smart.quality.evaluation.rule.title': 'Tambah formulir penilaian',
  'smart.quality.evaluation.rule.edit.title': 'Edit aturan',
  'smart.quality.evaluation.rule.deploy.status': 'Status penerapan',
  'smart.quality.evaluation.rule.deploy.status.unpublished':
    'Tidak dipublikasikan',
  'smart.quality.evaluation.rule.deploy.status.published': 'Dipublikasikan',
  'smart.quality.evaluation.rule.button.cancel': 'Batal',
  'smart.quality.evaluation.rule.button.save': 'Simpan',
  'smart.quality.evaluation.rule.button.save.and.publish':
    'Simpan dan publikasikan',
  'smart.quality.evaluation.rule.search.placeholder':
    'Masukkan nama aturan atau deskripsi untuk mencari',
  'smart.quality.evaluation.rule.add': 'Tambah aturan',
  'smart.quality.evaluation.rule.table.category': 'Kategori aturan',
  'smart.quality.evaluation.rule.table.name': 'Nama aturan',
  'smart.quality.evaluation.rule.table.description': 'Deskripsi aturan',
  'smart.quality.evaluation.rule.table.score': 'Aturan penilaian',
  'smart.quality.evaluation.rule.table.evaluation.method': 'Metode penilaian',
  'smart.quality.evaluation.rule.table.evaluation.method.manual':
    'Penilaian manual',
  'smart.quality.evaluation.rule.table.evaluation.method.ai': 'Penilaian AI',
  'smart.quality.evaluation.rule.table.operation': 'Tindakan',
  'smart.quality.evaluation.rule.table.operation.edit': 'Edit',
  'smart.quality.evaluation.rule.table.delete.confirm': 'Hapus aturan ini?',
  'smart.quality.evaluation.rule.table.delete.ok': 'Ya',
  'smart.quality.evaluation.rule.table.delete.cancel': 'Tidak',
  'smart.quality.evaluation.rule.table.operation.delete': 'Hapus',
  'smart.quality.evaluation.rule.table.operation.detail': 'Detail',
  'smart.quality.evaluation.rule.new.top.level': 'Kategori baru',
  'smart.quality.evaluation.rule.new.category': 'Kategori baru',
  'smart.quality.evaluation.rule.page.total.num': 'Total {total} item',
  'smart.quality.evaluation.rule.add.score.add': 'Tambah',
  'smart.quality.evaluation.rule.add.score.subtract': 'Kurangi',
  // end 规则列表
  // start 添加规则页面
  'smart.quality.evaluation.rule.add.title': 'Tambah aturan',
  'smart.quality.evaluation.rule.edit.title': 'Edit aturan',
  'smart.quality.evaluation.rule.detail.title': 'Detail aturan',
  'smart.quality.evaluation.rule.add.baseinfo': 'Informasi dasar',
  'smart.quality.evaluation.rule.add.baseinfo.name': 'Nama aturan',
  'smart.quality.evaluation.rule.add.baseinfo.name.placeholder':
    'Masukkan nama aturan',
  'smart.quality.evaluation.rule.add.baseinfo.name.required':
    'Masukkan nama aturan',
  'smart.quality.evaluation.rule.name': 'Nama aturan',
  'smart.quality.evaluation.rule.category.required': 'Pilih kategori aturan',
  'smart.quality.evaluation.rule.name.placeholder': 'Masukkan nama aturan',
  'smart.quality.evaluation.rule.name.required': 'Masukkan nama aturan',
  'smart.quality.evaluation.rule.name.max':
    'Panjang tidak boleh melebihi 80 karakter',
  'smart.quality.evaluation.rule.description': 'Deskripsi aturan',
  'smart.quality.evaluation.rule.description.placeholder':
    'Masukkan deskripsi aturan',
  'smart.quality.evaluation.rule.description.required':
    'Masukkan deskripsi aturan',
  'smart.quality.evaluation.rule.description.max':
    'Panjang tidak boleh melebihi 200 karakter',
  'smart.quality.evaluation.rule.category.placeholder': 'Pilih kategori aturan',
  'smart.quality.evaluation.rule.category.required': 'Pilih kategori aturan',
  'smart.quality.evaluation.rule.evaluation.method': 'Metode penilaian',
  'smart.quality.evaluation.rule.evaluation.method.ai': 'Penilaian AI',
  'smart.quality.evaluation.rule.evaluation.method.manual': 'Penilaian manual',
  'smart.quality.evaluation.rule.ai.settings': 'Aturan penilaian AI',
  'smart.quality.evaluation.rule.manual.settings': 'Aturan penilaian manual',
  'smart.quality.evaluation.rule.total.score': 'Total skor aturan',
  'smart.quality.evaluation.rule.total.score.placeholder':
    'Masukkan total skor aturan',
  'smart.quality.evaluation.rule.total.score.required':
    'Masukkan total skor aturan',
  'smart.quality.evaluation.rule.total.score.exceed.error':
    'Total skor aturan tidak boleh melebihi total skor formulir',
  'smart.quality.evaluation.rule.total.score.exceed.error.add':
    'Total poin tambahan aturan tidak boleh melebihi total skor formulir',
  'smart.quality.evaluation.rule.total.score.exceed.error.subtract':
    'Total poin dikurangi aturan tidak boleh melebihi total skor formulir',
  'smart.quality.evaluation.rule.aigc.rule1.score.exceed.error':
    'Skor aturan penilaian AI tidak boleh melebihi total skor aturan',
  'smart.quality.evaluation.rule.aigc.rule1.score.exceed.error.add':
    'Poin tambahan aturan penilaian AI tidak boleh melebihi total skor aturan',
  'smart.quality.evaluation.rule.aigc.rule1.score.exceed.error.subtract':
    'Poin dikurangi aturan penilaian AI tidak boleh melebihi total skor aturan',
  'smart.quality.evaluation.rule.aigc.rule2.score.exceed.error':
    'Skor aturan penilaian AI tidak boleh melebihi total skor aturan',
  'smart.quality.evaluation.rule.aigc.rule2.score.exceed.error.add':
    'Poin tambahan aturan penilaian AI tidak boleh melebihi total skor aturan',
  'smart.quality.evaluation.rule.aigc.rule2.score.exceed.error.subtract':
    'Poin dikurangi aturan penilaian AI tidak boleh melebihi total skor aturan',
  'smart.quality.evaluation.rule.manual.option.required':
    'Silakan tambahkan setidaknya satu aturan penilaian',
  'smart.quality.evaluation.rule.manual.option.save.first':
    'Silakan simpan aturan yang sedang Anda edit terlebih dahulu',
  'smart.quality.evaluation.rule.check.points': 'Checkpoint',
  'smart.quality.evaluation.rule.check.point.placeholder':
    'Masukkan deskripsi checkpoint yang detail',
  'smart.quality.evaluation.rule.check.point.required': 'Masukkan checkpoint',
  'smart.quality.evaluation.rule.add.check.point': 'Tambah checkpoint',
  'smart.quality.evaluation.rule.check.points.max':
    'Maksimal 15 checkpoint dapat ditambahkan',
  'smart.quality.evaluation.add.rule.button.return': 'Kembali',
  'smart.quality.evaluation.add.rule.button.save': 'Simpan',
  // end 添加规则页面
  // start 人工评分规则相关
  'smart.quality.evaluation.add.rule.manual.option.name': 'Nama opsi',
  'smart.quality.evaluation.add.rule.manual.option.name.required':
    'Masukkan nama opsi',
  'smart.quality.evaluation.add.rule.manual.option.name.placeholder':
    'Masukkan nama opsi',
  'smart.quality.evaluation.add.rule.manual.option.name.duplicate':
    'Nama opsi tidak boleh duplikat',
  'smart.quality.evaluation.add.rule.manual.option.score': 'Skor',
  'smart.quality.evaluation.add.rule.manual.option.score.required':
    'Masukkan skor',
  'smart.quality.evaluation.add.rule.manual.option.operation': 'Tindakan',
  'smart.quality.evaluation.add.rule.manual.rules': 'Aturan penilaian manual',
  'smart.quality.evaluation.add.rule.manual.add.option': 'Tambah opsi',
  'smart.quality.evaluation.add.rule.manual.standard':
    'Referensi kriteria evaluasi',
  'smart.quality.evaluation.add.rule.manual.standard.required':
    'Masukkan referensi kriteria evaluasi',
  'smart.quality.evaluation.add.rule.manual.standard.placeholder':
    'Masukkan referensi kriteria evaluasi',
  // end 人工评分规则相关
  // start AIGC评分规则相关
  'smart.quality.evaluation.add.rule.aigc.rules': 'Aturan penilaian AI',
  'smart.quality.evaluation.add.rule.aigc.rules.required':
    'Pilih aturan penilaian AI',
  'smart.quality.evaluation.add.rule.aigc.rule1.title':
    'Ketika salah satu checkpoint muncul',
  'smart.quality.evaluation.add.rule.aigc.rule1.prefix':
    ', untuk setiap kemunculan',
  'smart.quality.evaluation.add.rule.aigc.rule1.score.required':
    'Masukkan skor',
  'smart.quality.evaluation.add.rule.aigc.rule1.middle':
    'poin, dengan maksimal',
  'smart.quality.evaluation.add.rule.aigc.rule1.max.score.required':
    'Masukkan skor kumulatif maksimal',
  'smart.quality.evaluation.add.rule.aigc.rule1.suffix': 'poin',
  'smart.quality.evaluation.add.rule.aigc.rule2.title':
    'Ketika beberapa checkpoint muncul',
  'smart.quality.evaluation.add.rule.aigc.rule2.prefix':
    ', ketika jumlah kemunculan adalah',
  'smart.quality.evaluation.add.rule.aigc.rule2.condition.required':
    'Pilih jumlah kemunculan',
  'smart.quality.evaluation.add.rule.aigc.rule2.condition.gt':
    'Lebih besar dari',
  'smart.quality.evaluation.add.rule.aigc.rule2.condition.eq': 'Sama dengan',
  'smart.quality.evaluation.add.rule.aigc.rule2.condition.lt': 'Kurang dari',
  'smart.quality.evaluation.add.rule.aigc.rule2.threshold.required':
    'Masukkan jumlah kali',
  'smart.quality.evaluation.add.rule.aigc.rule2.middle': 'kali,',
  'smart.quality.evaluation.add.rule.aigc.rule2.score.required':
    'Masukkan skor',
  'smart.quality.evaluation.add.rule.aigc.rule2.suffix': 'skor',
  // end AIGC评分规则相关
};
