export default {
  'add.chat.channel.configuration.title':
    'Tambahkan saluran obrolan langsung di WEB',
  'add.chat.channel.configuration.title.update':
    'Perbarui saluran obrolan langsung di WEB',
  'app.add.chat.channel.configuration.title.update':
    'Perbarui saluran obrolan langsung di APLIKASI',
  'add.chat.channel.configuration.tips':
    'Sederhanakan interaksi pelanggan melalui situs WEB, memungkinkan respons yang lebih cepat dan efektif terhadap pertanyaan mereka.',
  'app.add.chat.channel.configuration.title':
    'Tambahkan saluran obrolan langsung di APLIKASI',
  'app.add.chat.channel.configuration.tips':
    'Sederhanakan interaksi pelanggan melalui situs APLIKASI, memungkinkan respons yang lebih cepat dan efektif terhadap pertanyaan mereka.',
  'chat.channel.configuration.title.1': 'Informasi dasar',
  'chat.channel.configuration.title.tips.1':
    '<PERSON>lakan isi informasi dasar saluran',
  'chat.channel.configuration.title.2': 'Pengaturan tampilan',
  'chat.channel.configuration.title.tips.2':
    'Di sini Anda dapat mengkustomisasi kotak obrolan, pilih LOGO dan skema warna favorit Anda',
  'chat.channel.configuration.title.3': 'Pengaturan fitur dasar',
  'chat.channel.configuration.title.tips.3':
    'Di sini Anda dapat mengonfigurasi fungsi dasar kotak obrolan',
  'chat.channel.configuration.chat3.whatsApp.message':
    'Setelah diaktifkan, ikon WhatsApp akan ditampilkan di bagian bawah jendela obrolan.',
  'chat.channel.configuration.chat3.whatsApp': 'Tampilkan saluran WhatsApp',
  'chat.channel.configuration.chat3.email.message':
    'Setelah diaktifkan, ikon email akan ditampilkan di bagian bawah jendela obrolan.',
  'chat.channel.configuration.chat3.email': 'Tampilkan saluran email',
  'chat.channel.configuration.chat3.email.select': 'Saluran email terkait',
  'chat.channel.configuration.chat3.email.select.placeholder':
    'Silakan pilih saluran email terkait',
  'chat.channel.configuration.chat3.email.select.message':
    'Setelah mengaitkan email, pengguna dapat langsung mengklik ikon email di bagian bawah kotak obrolan untuk kontak.',
  'chat.channel.configuration.chat3.WhatsApp.select':
    'Saluran WhatsApp terkait',
  'chat.channel.configuration.chat3.WhatsApp.select.placeholder':
    'Silakan pilih saluran WhatsApp terkait',
  'chat.channel.configuration.chat3.whatsApp.select.message':
    'Setelah mengaitkan nomor WhatsApp, pengguna dapat langsung mengklik ikon WhatsApp di bawah kotak obrolan untuk memulai percakapan WhatsApp',
  'chat.channel.configuration.title.4': 'Pengaturan layanan pelanggan cerdas',
  'chat.channel.configuration.title.tips.4':
    'Di sini Anda dapat mengonfigurasi informasi terkait layanan pelanggan cerdas',
  'chat.channel.configuration.title.5': 'Terapkan',
  'chat.channel.configuration.title.tips.5':
    'Silakan tambahkan kotak obrolan ke situs web Anda sesuai dengan deskripsi berikut',
  'chat.channel.configuration.channel.name': 'Nama saluran',
  'chat.channel.configuration.channel.name.placeholder':
    'Masukkan nama saluran',
  'chat.channel.configuration.chat.types': 'Bahasa',
  'chat.channel.configuration.chat.types.placeholder': 'Silakan pilih bahasa',
  'chat.channel.configuration.chat2.logo': 'Logo Perusahaan',
  'chat.channel.configuration.chat2.logo.message1':
    'Hanya JPG atau PNG yang didukung, dan ukuran gambar tidak boleh melebihi 500KB.',
  'chat.channel.configuration.chat2.logo.message2':
    'Ikon akan ditampilkan di sudut kiri atas kotak obrolan, disarankan untuk mengunggah gambar PNG 50*20px',
  'chat.channel.configuration.chat2.chatBoxName.placeholder':
    'Masukkan nama chatbot',
  'chat.channel.configuration.chat2.chatBoxName': 'Nama chatbot',
  'chat.channel.configuration.chat2.chatBoxName.message':
    'Akan ditampilkan di sudut kiri atas kotak obrolan',
  'chat.channel.configuration.chat2.templete': 'Warna tema',
  'chat.channel.configuration.chat2.templete.custom': 'Kustom',
  'chat.channel.configuration.chat2.templete.color': 'Warna saat ini：',
  'chat.channel.configuration.chat2.templete.placeholder':
    'Silakan pilih warna',
  'chat.channel.configuration.chat2.boxColor': 'Warna kotak obrolan agen',
  'chat.channel.configuration.chat2.userBox': 'Warna kotak obrolan pengguna',
  'chat.channel.configuration.chat2.information.configuration.completed':
    'Anda telah menyelesaikan pengaturan tampilan kotak obrolan, Anda dapat mengklik tombol pratinjau untuk melihat gaya.',
  'chat.channel.configuration.work.panels.checkbox': 'Aktifkan',
  'chat.channel.configuration.chat3.form':
    'Halaman formulir informasi pengguna',
  'chat.channel.configuration.chat3.form.message':
    'Disarankan untuk dibuka, setelah dibuka pengguna perlu memasukkan informasi dasar terlebih dahulu, baru dapat melakukan komunikasi selanjutnya',
  'chat.channel.configuration.chat3.welcome': 'Salam awal',
  'chat.channel.configuration.chat3.welcome.words': 'Salam singkat',
  'chat.channel.configuration.chat3.welcome.words.placeholder':
    'Silakan masukkan pesan sambutan',
  'chat.channel.configuration.chat3.interval.placeholder':
    'Silakan masukkan waktu sesi undangan otomatis',
  'chat.channel.configuration.chat3.welcome.words.message':
    'Pengaturan di sini harap sesuai dengan bahasa yang Anda pilih di langkah pertama',
  'chat.channel.configuration.chat3.welcome.QA': 'Picu FAQ',
  'chat.channel.configuration.chat3.welcome.QA.placeholder':
    'Silakan pilih FAQ yang sesuai.',
  'chat.channel.configuration.chat3.welcome.QA.message':
    'FAQ memungkinkan Anda membalas beberapa jawaban kepada pengguna sekaligus, tidak hanya mendukung teks, tetapi juga mendukung gambar dan video. Jika Anda memilih FAQ tertentu, saat pengguna menghubungi Anda, sistem akan secara otomatis membalas dengan jawaban standar yang dikonfigurasi dalam FAQ. Jika Anda belum menyiapkan FAQ apa pun, Anda dapat mengklik',
  'chat.channel.configuration.chat3.welcome.QA.message.1': ' di sini ',
  'chat.channel.configuration.chat3.welcome.QA.message.2':
    'untuk menyiapkannya.',
  'chat.channel.configuration.chat3.talk': 'Popup obrolan otomatis',
  'chat.channel.configuration.chat3.talk.ge': 'Interval',
  'chat.channel.configuration.chat3.talk.ge2':
    'detik, sistem akan secara otomatis memunculkan kotak obrolan',
  'chat.channel.configuration.chat3.message':
    'Ketika pelanggan menjelajahi situs web Anda, sistem akan secara otomatis memicu jendela obrolan untuk secara aktif mengundang pelanggan untuk konsultasi.',
  'chat.channel.configuration.chat3.voice.message':
    'Pelanggan dapat mengklik tombol video di bawah jendela obrolan untuk terlibat dalam komunikasi suara dengan agen.',
  'chat.channel.configuration.chat3.voice': 'Komunikasi suara online',
  'chat.channel.configuration.chat3.video.message':
    'Pelanggan dapat mengklik tombol video di bawah jendela obrolan untuk terlibat dalam komunikasi video dengan agen.',
  'chat.channel.configuration.chat3.video': 'Komunikasi video online',
  'chat.channel.configuration.chat3.evaluate.message':
    'Setelah mengakhiri obrolan, sistem secara otomatis memunculkan evaluasi kepuasan untuk agen.',
  'chat.channel.configuration.chat3.evaluate': 'Evaluasi kepuasan',
  'chat.channel.configuration.chat3.information.configuration.completed':
    'Anda telah menyelesaikan pengaturan fungsionalitas dasar, ini akan diimplementasikan dalam kotak obrolan setelah Anda menyimpan',
  'chat.channel.configuration.chat4.mode.message': `Pilih "Layanan pelanggan cerdas", akan ada respons robotik terhadap pertanyaan pelanggan terlebih dahulu, jika chatbot tidak dapat menjawab, pengguna dapat beralih ke layanan agen manusia kapan saja.`,
  'chat.channel.configuration.chat4.mode.message.1': ` Pilih "Hanya agen", hanya agen manusia yang akan menjawab pertanyaan pelanggan.`,
  'chat.channel.configuration.chat4.mode.message.2': `Pilih "Hanya chatbot", hanya layanan pelanggan robotik yang akan menjawab pertanyaan pelanggan.`,
  'chat.channel.configuration.chat4.mode.1': 'Layanan pelanggan cerdas',
  'chat.channel.configuration.chat4.mode.2': 'Hanya agen',
  'chat.channel.configuration.chat4.mode.3': 'Hanya chatbot',
  'chat.channel.configuration.chat4.mode': 'Mode layanan pelanggan',
  'chat.channel.configuration.chat4.robot.message':
    'Nama chatbot akan ditampilkan di atas jawaban chatbot',
  'chat.channel.configuration.chat4.robot.placeholder': 'Masukkan nama bot',
  'chat.channel.configuration.chat4.robot': 'Nama chatbot',
  'chat.channel.configuration.chat4.language.message': `Dengan fitur ini diaktifkan, sistem akan secara otomatis mengidentifikasi bahasa dari pertanyaan input pengguna; jika tidak diaktifkan, akan menggunakan bahasa browser pengguna secara default.`,
  'chat.channel.configuration.chat4.language': 'Identifikasi bahasa otomatis',
  'chat.channel.configuration.chat4.document': 'Basis pengetahuan dokumen',
  'chat.channel.configuration.chat4.document.placeholder':
    'Silakan pilih basis pengetahuan dokumen',
  'chat.channel.configuration.chat4.document.message.1':
    'Basis pengetahuan dokumen ini hanya menampilkan basis pengetahuan eksternal, silakan konfigurasikan basis pengetahuan di ',
  'chat.channel.configuration.chat4.document.message':
    ' Basis pengetahuan dokumen',
  'chat.channel.configuration.chat4.document.message.2': 'halaman.',
  'chat.channel.configuration.chat4.ai.message':
    'Anda dapat mengonfigurasi apakah akan mengaktifkan AI Generatif.',
  'chat.channel.configuration.chat4.ai': 'Mengintegrasikan AI Generatif',
  'chat.channel.configuration.chat4.workers': `Tampilkan tombol "Transfer ke agen" untuk pertanyaan yang tidak diketahui`,
  'chat.channel.configuration.chat4.workers.message': `Jika diaktifkan, ketika chatbot tidak mengetahui jawabannya, akan secara otomatis menampilkan tombol "Transfer ke agen" di bawah konten respons chatbot`,
  'chat.channel.configuration.chat4.unknown':
    'Respons chatbot saat menghadapi pertanyaan yang tidak diketahui',
  'chat.channel.configuration.chat4.unknown.placeholder':
    'Silakan masukkan balasan chatbot untuk pertanyaan yang tidak diketahui',
  'chat.channel.configuration.chat4.unknown.message':
    'Pengaturan ini adalah respons chatbot saat menghadapi pertanyaan yang tidak diketahui',
  'chat.channel.configuration.chat4.information.configuration.completed':
    'Anda telah menyelesaikan pengaturan layanan pelanggan cerdas, ini akan diimplementasikan dalam kotak obrolan setelah Anda menyimpan.',
  'chat.channel.configuration.chat5.message':
    'Salin kode berikut dan masukkan dalam tag <body> </body> di situs web Anda.',
  'chat.channel.configuration.chat5.message.link':
    'Tautan obrolan: Salin tautan berikut ke dalam kode situs web Anda',
  'live.chat.title': 'Area pratinjau kotak obrolan',
  'live.chat.title.subtitle':
    'Di sini Anda dapat melihat pratinjau efek kotak obrolan',
  'live.chat.customer': 'Pelanggan',
  'live.chat.customer.Dialogue':
    'Bisakah Anda memberi tahu saya apa saja fitur utama produk ini?',
  'live.chat.submit': 'Mari mengobrol',
  'live.chat.end': 'Akhir Percakapan',
  'live.chat.video': 'Panggilan Video',
  'chat.channel.configuration.cancel.btn': 'Batal',
  'chat.channel.configuration.next.btn': 'Langkah selanjutnya',
  'chat.channel.configuration.complete.btn': 'Selesai',
  'chat.channel.configuration.title.knowledge_unknown_reply':
    'Dengan keterampilan saya saat ini, saya tidak dapat menjawab pertanyaan yang Anda ajukan. Jika diperlukan, Anda dapat langsung memilih agen manusia kami untuk dukungan yang lebih profesional😊',
  'chat.channel.configuration.chat5.end': `Harap dicatat: Setelah mengintegrasikan kode di atas ke dalam situs web Anda, silakan hubungi administrator "ConnectNow" untuk menambahkan domain yang ditentukan ke daftar putih. Komponen obrolan hanya akan ditampilkan dengan benar setelah konfigurasi daftar putih selesai.`,
  'chat.channel.configuration.chat5.end.1': ` `,
  'chat.channel.configuration.channel.name.web': 'Domain situs web',
  'chat.channel.configuration.channel.name.placeholder.web':
    'Silakan masukkan nama domain situs web',
  'chat.channel.configuration.chat4.workers.content':
    'Maaf, saya tidak dapat menjawab pertanyaan ini untuk Anda, silakan hubungi dukungan pelanggan.',
  'chat.channel.configuration.chat4.workers.position': 'Lokasi',
  'chat.channel.configuration.chat4.workers.zhuan': 'Transfer ke Agen',
  'live.chat.customer.Dialogue.product': 'Produk mana yang ingin Anda ketahui?',
  'chat.channel.configuration.chat4.workers.position.zhuan': 'Transfer ke Agen',
  'chat.channel.configuration.chat5.message.Settings': 'Pengaturan penyebaran',
  'chat.channel.configuration.channel.name.placeholder.error':
    'Hanya dapat memasukkan karakter Cina, huruf besar dan kecil, angka, "-" dan "_"',
  'chat.channel.configuration.channel.chatBoxName.placeholder.error':
    'Hanya karakter Cina, huruf besar dan kecil, spasi yang diizinkan',
  'chat.channel.configuration.chat1.document.placeholder.language':
    'Pemicu perubahan data FAQ Silakan pilih lagi',
  'chat.channel.configuration.channel.website':
    'Format nama domain situs web adalah sebagai berikut: www.connectnow.cn',
  'chat.channel.configuration.channel.website.name.placeholder.error':
    'Silakan masukkan domain situs web sesuai aturan',
  'chat.channel.configuration.work.panels.checkbox.ccp':
    'Apakah akan mengaktifkan pengenalan bahasa',
  'chat.channel.configuration.chat3.talk.Input': 'Pesan popup otomatis',
  'chat.channel.configuration.chat3.talk.Input.placeholder':
    'Silakan masukkan pesan selamat datang percakapan undangan otomatis',
  'chat.channel.configuration.chat3.talk.Input.message':
    'Atur pesan selamat datang yang ditampilkan saat undangan otomatis untuk obrolan muncul di sini.',
  'chat.channel.configuration.title.pop_welcome_msg':
    'Halo, saya adalah layanan pelanggan cerdas ConnectNow, ada yang bisa saya bantu?',
  'chat.channel.configuration.chat4.workers.keyword.message':
    'Ketika pelanggan memasukkan kata kunci ini, sistem akan secara otomatis mentransfer ke agen layanan pelanggan manusia.',
  'chat.channel.configuration.chat4.workers.keyword':
    'Kata Kunci Transfer ke Agen',
  'chat.channel.configuration.chat4.document.placeholder.keyword':
    'Silakan masukkan setidaknya satu kata kunci',
  'wx.program.channel.configuration.title':
    'Tambah saluran obrolan Program Mini WeChat',
  'wx.program.channel.configuration.title.update':
    'Perbarui saluran obrolan Program Mini WeChat',
  'wx.program.channel.configuration.tips':
    'Sederhanakan interaksi pelanggan melalui Program Mini WeChat, memungkinkan respons yang lebih cepat dan efektif terhadap pertanyaan mereka.',
  'shopify.channel.configuration.title': 'Tambah saluran obrolan Shopify',
  'shopify.channel.configuration.title.update':
    'Perbarui saluran obrolan Shopify',
  'shopify.channel.configuration.tips':
    'Sederhanakan interaksi pelanggan melalui situs web Shopify, memungkinkan respons yang lebih cepat dan efektif terhadap pertanyaan mereka.',
  'chat.channel.configuration.chat1.ai.aiagent': 'Agen AI',
  'chat.channel.configuration.chat1.ai.aiagent.tips':
    'Setelah mengaktifkan Agen AI, Anda perlu pergi ke halaman Agen AI untuk mengonfigurasi pesan selamat datang dan memilih basis pengetahuan AIGC.',
  'chat.channel.configuration.chat2.agent.avac': 'Avatar agen default',
  'chat.channel.configuration.chat2.customer.avac': 'Avatar pelanggan default',
  'chat.channel.configuration.chat2.robot.avac': 'Avatar robot',
  'chat.channel.configuration.chat2.robot.avac.tips':
    'Akan ditampilkan di kotak obrolan, disarankan untuk mengunggah gambar PNG 50*50px',
  'chat.channel.configuration.chat2.agent.font.color':
    'Warna font obrolan agen',
  'chat.channel.configuration.chat2.robot.avac.kexuan': '(Opsional)',
  'chat.channel.configuration.chat2.customer.font.color':
    'Warna font obrolan pengguna',
  'chat.channel.configuration.chat3.history.chat': 'Tampilkan dialog riwayat',
  'chat.channel.configuration.chat3.history.chat.tips':
    'Setelah mengaktifkan ini, pengguna akhir akan dapat melihat riwayat percakapan sebelumnya.',
  'chat.channel.configuration.chat3.history.chat.num':
    'Jumlah percakapan historis',
  'chat.channel.configuration.chat3.history.chat.num.p':
    'Silakan pilih jumlah percakapan historis yang akan ditampilkan',
  'chat.channel.configuration.chat3.history.chat.select': 'Tanpa batasan',
  'chat.channel.configuration.chat4.transfor.btn':
    'Tampilkan tombol transfer ke manusia',
  'chat.channel.configuration.chat4.transfor.btn.tips':
    'Setelah diaktifkan, tombol "Transfer ke Agen" akan ditampilkan di sisi kanan kotak obrolan',
  'chat.channel.configuration.chat4.agent.workTime': 'Waktu kerja agen',
  'chat.channel.configuration.chat4.agent.workTime.p':
    'Silakan pilih waktu kerja agen',
  'chat.channel.configuration.chat4.agent.workTime.no':
    'Pesan respons untuk jam non-kerja',
  'chat.channel.configuration.chat4.agent.workTime.no.default': `Maaf, waktu saat ini tidak dalam jam kerja staf kami. Silakan kembali untuk berkonsultasi dengan kami antara pukul 8:00 pagi dan 6:00 sore.`,
  'chat.channel.configuration.chat4.agent.workTime.no.tips': `Respons robot ketika pelanggan meminta untuk berbicara dengan agen di luar jam kerja.`,
  'chat.channel.configuration.chat1.push.agents.join.chat':
    'Dorong agen untuk bergabung dalam obrolan',
  // discord渠道
  'channel.allocation.detail.discord.title': 'Konfigurasi saluran Discord',
  'add.discord.channel.configuration.title': 'Tambah saluran Discord',
  'editor.discord.channel.configuration.title': 'Edit saluran Discord',
  'add.discord.channel.configuration.tips': 'Silakan tambahkan saluran Discord',
  'discord.channel.configuration.title.1': 'Konfigurasikan Token Bot Discord',
  'discord.channel.configuration.title.tips.1':
    'Masukkan Token Bot dari Portal Pengembang Discord ke dalam bidang input di bawah ini',
  'discord.channel.configuration.title.tips.2': 'Lihat dokumen konfigurasi',
  'discord.channel.allocation.complete.1':
    'Anda telah berhasil memasukkan Token Bot, silakan lanjutkan untuk menyelesaikan konfigurasi chatbot',
  'discord.channel.configuration.channel.bot.name': 'Nama bot',
  'discord.channel.configuration.channel.bot.name.placeholder':
    'Silakan masukkan nama bot',
  'discord.channel.configuration.channel.bot.token': 'Token Bot',
  'discord.channel.configuration.channel.bot.token.placeholder':
    'Silakan masukkan Token Bot',
  'channel.allocation.detail.input.bot.name': 'Nama bot:',
  'channel.allocation.detail.input.bot.name.placeholder':
    'Masukkan nama bot dan tekan Enter untuk mencari',
  'discord.channel.configuration.channel.application.id': 'ID Aplikasi:',
  'discord.channel.configuration.channel.application.id.placeholder':
    'Silakan masukkan ID Aplikasi',
  // discord帮助文档
  'channel.allocation.discord.document.title':
    'Discord: Integrasi dengan ConnectNow',
  'channel.allocation.discord.document.h1': 'Siapkan server Discord',
  'channel.allocation.discord.document.step.1': ' Siapkan akun Discord',
  'channel.allocation.discord.document.step.1.text': `Jika Anda tidak memiliki akun Discord, silakan kunjungi <a>https://discord.com/</a> untuk mendaftar`,
  'channel.allocation.discord.document.step.1.text.1':
    ' Jika Anda sudah memiliki akun Discord, silakan langsung ke langkah dua',
  'channel.allocation.discord.document.step.2': ' Siapkan server Discord',
  'channel.allocation.discord.document.step.2.text':
    'Catatan: Jika Anda sudah memiliki server Discord, silakan lewati langkah ini',
  'channel.allocation.discord.document.step.2.text.1': ' Klik Buat Server',
  'channel.allocation.discord.document.step.2.text.2': ' Pilih Buat Milik Saya',
  'channel.allocation.discord.document.step.2.text.3': `Pilih informasi server Anda berdasarkan kebutuhan Anda. Jika Anda tidak yakin, Anda juga dapat mengklik "Lewati pertanyaan ini"`,
  'channel.allocation.discord.document.step.2.text.4':
    'Masukkan nama server Anda, unggah logo, lalu klik Buat',
  'channel.allocation.discord.document.step.3': ' Buat aplikasi',
  'channel.allocation.discord.document.step.3.text':
    'Kunjungi Portal Pengembang <a>https://discord.com/developers/applications</a> dan klik Aplikasi Baru',
  'channel.allocation.discord.document.step.3.text.1':
    'Masukkan nama aplikasi dan klik Buat',
  'channel.allocation.discord.document.step.3.text.2':
    'Setelah pembuatan selesai, atur informasi aplikasi Anda',
  'channel.allocation.discord.document.step.3.text.3': 'Klik Bot di menu kiri',
  'channel.allocation.discord.document.step.3.text.4':
    'Konfigurasikan izin Bot. Ikuti gambar di bawah ini untuk mengonfigurasi izin, lalu klik tombol [Simpan Perubahan]',
  'channel.allocation.discord.document.step.3.text.5':
    'Otorisasi. Klik OAuth2 di menu kiri, gulir ke bawah, centang Bot, lalu gulir ke bawah dan secara berurutan centang Kirim Pesan, Kirim Pesan dalam Thread, Kelola Pesan, Baca Riwayat Pesan, dan Lihat Saluran. Lihat dua gambar di bawah ini',
  'channel.allocation.discord.document.step.3.text.6':
    'Setelah membuat pilihan, tautan otorisasi akan dibuat di bagian bawah halaman. Klik untuk menyalinnya dan buka di browser',
  'channel.allocation.discord.document.step.3.text.7':
    'Setelah membuka tautan, seperti yang ditunjukkan di bawah ini, pilih server untuk menambahkan bot, klik Lanjutkan, lalu klik tombol Otorisasi pada langkah berikutnya',
  'channel.allocation.discord.document.step.3.text.8':
    'Bot berhasil ditambahkan ke server',
  'channel.allocation.discord.document.step.4':
    ' Integrasikan bot ke ConnectNow',
  'channel.allocation.discord.document.step.4.text':
    'Dapatkan Token Bot. Kunjungi Portal Pengembang <a>https://discord.com/developers/applications</a>, klik Bot di menu kiri, dan klik Reset Token',
  'channel.allocation.discord.document.step.4.text.1':
    'Setelah dibuat, klik Salin untuk menyimpan Token',
  'channel.allocation.discord.document.step.4.text.6':
    'Dapatkan ID Aplikasi. Bidang ini sangat penting, kesalahan akan menyebabkan saluran tidak menerima pesan!',
  'channel.allocation.discord.document.step.4.text.2':
    'Pergi ke admin ConnectNow, klik Saluran -> Discord -> Tambah Saluran, masukkan Token yang baru saja Anda simpan dan nama bot, lalu klik Berikutnya',
  'channel.allocation.discord.document.step.4.text.3':
    'Atur parameter chatbot sesuai kebutuhan Anda, lalu klik Berikutnya',
  'channel.allocation.discord.document.step.4.text.4': 'Masukkan nama saluran',
  'channel.allocation.discord.document.step.4.text.5':
    '🎉Selamat! Discord Anda telah berhasil diintegrasikan dengan ConnectNow',
};
