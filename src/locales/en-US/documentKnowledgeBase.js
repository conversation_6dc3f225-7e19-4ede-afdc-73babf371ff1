export default {
  'document.knowledge.base.title': 'Document',
  'document.knowledge.base.add.btn': 'Add',
  'document.knowledge.base.internal': 'Internal',
  'document.knowledge.base.external': 'External',
  'document.knowledge.base.number.documents': 'Docs: ',
  'document.knowledge.base.select.language': 'Language:',
  'document.knowledge.base.input.tips': 'Enter the content you want to search',
  'document.knowledge.base.input.tips.1': 'Please enter the document name',
  'document.knowledge.base.space': 'Knowledge Base Storage Used:',
  'document.knowledge.base.space.word': 'words',
  'document.knowledge.base.space.word.sum': 'Words Used:',
  'document.knowledge.base.space.word.popover': `Your current version allows a total of {countNum} uploads, with {proportion} already used.`,

  'document.knowledge.base.upload.btn': 'Upload document',
  'document.knowledge.base.upload.synchronization.btn': 'Upload',
  'document.knowledge.base.synchronization.btn': 'Sync',
  'document.knowledge.base.table.document.name': 'Name',
  'document.knowledge.base.table.document.name.rule': 'Rule name',

  'document.knowledge.base.table.document.language': 'Language',
  'document.knowledge.base.table.creator': 'Creator',
  'document.knowledge.base.table.creation.time': 'Creation time',
  'document.knowledge.base.table.operation': 'Operation',
  'document.knowledge.base.table.operation.download': 'Download',
  'document.knowledge.base.table.operation.preview': 'Preview',
  'document.knowledge.base.table.operation.upload': 'Re-upload',
  'document.knowledge.base.table.operation.delete': 'Delete',
  'document.knowledge.base.table.operation.Stop': 'Stop',

  'document.knowledge.base.table.operation.upload.Popconfirm':
    'Are you sure you want to recrawl this data?',
  'document.knowledge.base.table.operation.delete.Popconfirm':
    'Are you sure you want to delete this data?',
  'document.knowledge.base.table.operation.Stop.Popconfirm':
    'Are you sure you want to stop crawling this data?',

  'document.knowledge.base.modal.title': 'Adding knowledge base',
  'document.knowledge.base.external.text':
    'The external knowledge base is mainly provided for end customers;',
  'document.knowledge.base.external.text.tips':
    'Agents can also retrieve knowledge from external knowledge bases.',
  'document.knowledge.base.internal.text':
    'The internal knowledge base is only open for the internal agents of your enterprise.',
  'document.knowledge.base.modal.knowledge.name': 'Name:',
  'document.knowledge.base.modal.knowledge.name.required':
    'Enter knowledge base name',
  'document.knowledge.base.modal.knowledge.introduction': 'Description:',
  'document.knowledge.base.modal.knowledge.introduction.required':
    'Enter the description of knowledge base',
  'document.knowledge.base.modal.editor.title': 'Updating knowledge base',
  'document.knowledge.base.upload.tips':
    'Drag and drop files here or<b>{US}</b>. It supports single or batch uploading. Currently it supports PDF, TXT, XLSX, DOCX, MD.',
  'document.knowledge.base.upload.tips.1':
    'Drag and drop files here or<b>{US}</b>. It supports single or batch uploading. Currently it supports PDF.',
  'document.knowledge.base.upload.tips1':
    'The maximum supported document size is 100MB',
  'document.knowledge.base.upload.tips2':
    ' with a maximum text extraction limit of 5MB per individual document,',
  'document.knowledge.base.upload.tips3':
    ' Exceeding this limit will result in the failure to synchronize the document with the knowledge base.',
  'document.knowledge.base.table.total.title':
    'File list (total <b>{US}</b> files)',
  'document.knowledge.base.batch.deletion.btn': 'Batch deletion',
  'document.knowledge.base.table.document.format': 'Format',
  'document.knowledge.base.table.document.file.size': 'File size',
  'document.knowledge.base.table.upload.status': 'Upload status',
  'document.knowledge.base.table.upload.success': 'Upload successful',
  'document.knowledge.base.table.upload.error': 'Upload failed',
  'document.knowledge.base.table.upload.to.be': 'To be uploaded',
  'document.knowledge.base.table.uploading': 'Uploading',
  'document.knowledge.base.save.list.btn': 'Upload',
  'document.knowledge.base.return.list.btn': 'Return List',
  'document.knowledge.base.name.length.tips':
    'The knowledge base name can be up to 40 characters at most',
  'document.knowledge.base.name.format.tips':
    'The knowledge base name can only contain English, numbers, and hyphens (excluding Chinese and spaces)',
  'document.knowledge.base.knowledge.introduction.length.tips':
    'Knowledge base introduction can input up to 1000 characters',
  'document.knowledge.base.delete.tips':
    'This knowledge base contains <b>{US}</b> articles. Deleting the knowledge base will delete all articles. Are you sure you want to delete?',
  'document.knowledge.base.click.delete.required':
    'Enter "Delete" and click OK',

  'document.knowledge.base.select.knowledge.type.tips':
    'The knowledge base type is a required option and cannot be empty',
  'document.knowledge.base.create.knowledge.status': 'Creating',
  'document.knowledge.base.synchronizing.knowledge.status': 'Synchronizing...',
  'document.knowledge.base.synchronizing.knowledge.status.no': 'Synchronizing',
  'document.knowledge.base.table.synchronization.status':
    'Synchronization status',
  'document.knowledge.base.table.synchronization.status.placeholder':
    'Please select the Synchronization status',
  'document.knowledge.base.table.synchronized.success': 'Synchronized',
  'document.knowledge.base.table.not.synchronized.success': 'Not synchronized',
  'document.knowledge.base.table.not.synchronized.queuing': 'In Queue',
  'document.knowledge.base.table.not.synchronized.limit':
    'The character count has reached the limit',

  'document.knowledge.base.table.synchronized.fail': 'Sync failed',
  'document.knowledge.base.table.synchronized.success.synchronizing':
    'Synchronized : <span>{docCount}</span> / All: <span>{docCount1}</span>',
  'document.knowledge.base.create.knowledge.tips':
    'Creating, expected to take ten minutes',
  'contact.customers.basic.add.icon.tips': 'Add Knowledge Base',
  'contact.customers.basic.refresh.icon.tips': 'Refresh',
  'contact.customers.upload.files.number':
    'Only a maximum of 100 files can be uploaded!',
  'document.knowledge.base.add.tips':
    'Tips: Creating a knowledge base will incur corresponding resource costs',
  'web.scraping.title.1': 'Web Scraping Rule Name',
  'web.scraping.title.2': 'Web Scraping Address',
  'web.scraping.title.3': 'Web Scraping Rule',
  'web.scraping.title.4': 'Web Scraping Frequency',
  'web.scraping.title.2.tips': 'A maximum of 50 webpages can be entered',
  'web.scraping.form.label.1': 'Web crawl rule name',
  'web.scraping.form.label.1.placeholder':
    'Please enter the name of the web crawling rule',
  'web.scraping.form.label.1.placeholder.2':
    'Please select the name of the web crawling rule',
  'web.scraping.form.label.2': 'Website address',
  'web.scraping.form.label.2.placeholder': 'Please enter the website address',
  'web.scraping.form.label.2.button': 'Add address',
  'web.scraping.form.label.3':
    'Only crawl web pages within the current main domain',
  'web.scraping.form.label.4': 'Data update rules',
  'web.scraping.form.label.4.radio.1': 'Only update changed webpages',
  'web.scraping.form.label.4.radio.2': 'Update all webpages',
  'web.scraping.form.label.5': 'Depth of web crawling',
  'web.scraping.form.label.5.placeholder': 'Enter an integer between 1-5',
  'web.scraping.form.label.6': 'Frequency of web crawling',
  'web.scraping.form.label.7':
    'Limit crawling to only pages starting with the "current page address prefix"',
  'web.scraping.form.label.8': 'Maximum number of webpages to crawl',
  'web.scraping.form.label.8.placeholder':
    'Please enter an integer between 1 and 500,000',
  'web.scraping.form.label.9': 'Crawl content type:',
  'web.scraping.form.label.9.placeholder':
    'The content type for crawling must not be empty',
  'web.scraping.form.label.9.options.1': 'Text',
  'web.scraping.form.label.9.options.2': 'Picture',
  'web.scraping.form.label.9.options.3': 'PDF',
  'web.scraping.form.label.9.options.4': 'Excel',
  'web.scraping.form.label.10': 'Website area',
  'web.scraping.form.label.10.options.1': 'Global website',
  'web.scraping.form.label.10.options.2': 'China Region Website',

  'web.scraping.form.save': 'Save and crawl',
  'web.scraping.file.detail.title': 'Crawl file detail',
  'web.scraping.file.detail.input.label': 'Web link',
  'web.scraping.file.detail.input.label.placeholder':
    'Please enter the webpage link you want to search',
  'web.scraping.file.detail.table': 'Update time',
  'web.scraping.file.detail.table.op': 'Recrawl',
  'web.scraping': 'Web scraping',
  'web.scraping.table.2': 'Number of website address',
  'web.scraping.table.3': 'Number of documents crawled',
  'web.scraping.table.4': 'Crawl frequency',
  'web.scraping.table.5': 'Crawl status',
  'web.scraping.tabs.1': 'Upload documents',
  'web.scraping.tabs.ocr': 'Upload OCR',
  'web.scraping.select.options.1': 'On-demand',
  'web.scraping.select.options.2': 'Scheduled crawling',
  'web.scraping.form.label.2.placeholder.rule':
    'Please enter at least one website address',
  'web.scraping.form.label.2.placeholder.rule.address':
    'Please enter the URL of the rule',
  'web.scraping.tabs.1.learn': 'Document Learning',
  'web.scraping.tabs.2.learn': 'OCR Learning',
  'web.scraping.tabs.3.learn': 'Web Learning',
  'web.scraping.table.crawlersStatus.status.1': 'To be crawled',
  'web.scraping.table.crawlersStatus.status.2': 'Crawling',
  'web.scraping.table.crawlersStatus.status.3': 'Completed',
  'web.scraping.table.crawlersStatus.status.4': 'Terminated',
  'web.scraping.every.date.text.1': 'Automatically crawls every',
  'web.scraping.every.date.text.2': 'days',
  'web.scraping.every.date.text.1.placeholder':
    'Please enter a positive integer between 1 and 365',

  'document.knowledge.base.table.confirm.text':
    'Are you sure you want to delete this document?',

  // 新版创建知识库
  'new.create.knowledge.info.1':
    'You can set the basic information of the knowledge base',
  'new.create.knowledge.roles.permissions': 'Roles and Permissions',
  'new.create.knowledge.info.2':
    'You can set roles and permissions for the knowledge base.',
  'new.create.knowledge.type.tips': 'Please select the knowledge base type',
  'new.create.knowledge.type.text': 'Knowledge base type:',
  'new.create.knowledge.editor.status': 'Editable',
  'new.create.knowledge.editor.view': 'Can view',
  'new.create.knowledge.knowledge.base.permissions.admin':
    'Permissions: Can manage knowledge base',
  'new.create.knowledge.knowledge.base.permissions.editor':
    'Permissions: can upload, delete knowledge documents',
  'new.create.knowledge.knowledge.base.permissions.view':
    'Permissions: Can view knowledge base',
  'new.create.knowledge.add.admin.btn': 'Add administrator',
  'new.create.knowledge.add.user.btn': 'Add user',
  'new.create.knowledge.customer.has.check': 'Selected: ',
  'new.create.knowledge.no.data.tips':
    'Failed to get knowledge base ID, please return to the previous page',
  'new.create.knowledge.delete.user.waring':
    'The current operation does not allow you to remove yourself from the administrator list',
  'new.create.knowledge.delete.user.waring.1':
    'There must be at least one administrator in the knowledge base',
  'document.knowledge.upload.file.name.tips':
    'The filename is not allowed to contain special characters!',

  'web.scraping.table.6': 'Is valid',
  'web.scraping.table.5.valid': 'Valid',
  'web.scraping.table.5.not': 'Invalid',
  'web.scraping.select.effective.period': 'Knowledge validity:',
  'document.knowledge.base.upload.document.tips':
    'If you enable knowledge base classification, you can select the corresponding classification dimension value when uploading documents',
  'document.knowledge.base.upload.effective.period': 'Effective period:',
  'document.knowledge.base.upload.file.tips': 'Please upload file',
  'document.knowledge.base.upload.effective.period.tips':
    'Please select the effective period',
};
