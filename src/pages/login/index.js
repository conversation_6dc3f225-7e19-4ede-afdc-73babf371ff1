import React, { Component } from 'react';
import {
  connect,
  getIntl,
  Link,
  FormattedMessage,
  history,
  getLocale,
  setLocale,
} from 'umi';
import { Button, Checkbox, Form, Input, Row, Col, Dropdown } from 'antd';
import cookie from 'react-cookies';
import Captcha from 'react-captcha-code';
import {
  LockOutlined,
  UserOutlined,
  SafetyCertificateOutlined,
  ArrowRightOutlined,
} from '@ant-design/icons';
import { notification } from '@/utils/utils';
import crypto from '@/utils/encrypt';
import styles from './index.less';
import LogoImg from '../../assets/logo.png';
import encrypt from '../../utils/encrypt';
import CnIcon from '../../assets/Cn.svg';
import EnIcon from '../../assets/En.svg';
import leftImg from '../../assets/newLogin.png';
import leftImgEn from '../../assets/newLogin2.png';
import ChatWidget from '../../components/chatWidget';
import LangIcon from '../../assets/lang-icon.png';
import DropLanguage from '../dropLanguage/index';

import SliderCaptch from 'rc-slider-captcha';
import createPuzzle from 'create-puzzle';
import ImageBg1 from '../../assets/1.png';
import ImageBg2 from '../../assets/2.png';
import ImageBg3 from '../../assets/3.png';
import ImageBg4 from '../../assets/4.png';
import ImageBg5 from '../../assets/5.png';
import ImageBg6 from '../../assets/6.png';
import ImageBg7 from '../../assets/7.png';
import ImageBg8 from '../../assets/8.png';
import ImageBg9 from '../../assets/9.png';
import ImageBg10 from '../../assets/10.png';

import LoginReturn from '../../assets/login-return.png';
import LanguageEnUS from '../../locales/language-en-US.json';
import LanguageZhCN from '../../locales/language-zh-CN.json';
import LanguageDeDE from '../../locales/language-de-DE.json';
import LanguageJa from '@/locales/language-ja.json';

// import LoginCodeIcon from '../../assets/login-code-icon.svg';
import CookieConsent from '@/components/CookieConsent';
const LoginCodeIcon = () => (
  <svg
    width="400"
    height="160"
    viewBox="0 0 400 160"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      fill-rule="evenodd"
      clip-rule="evenodd"
      d="M366.645 28.0217C366.462 27.874 366.261 27.7128 366.045 27.5354H374.402C375.855 27.5354 376.94 27.6256 377.667 27.8143C379.119 28.0029 380.205 28.4622 380.756 29.1183C381.298 29.8647 381.57 30.9802 381.57 32.6615C381.57 33.8754 381.386 36.4917 380.844 40.5926L380.029 46.6538H400C399.458 50.7629 399.003 53.371 398.635 54.6833C398.189 56.5451 397.366 57.759 396.281 58.5053C395.554 58.9728 394.557 59.3419 393.104 59.5306C391.827 59.6208 389.289 59.7192 385.29 59.7192H378.209L377.754 63.1722L375.943 76.3196L375.488 79.6824H395.459L395.442 79.8002C394.909 83.6551 394.553 86.2241 394.19 87.5151C393.83 89.1062 393.192 90.3201 392.465 91.0664C391.739 91.8128 390.558 92.2721 388.921 92.5592C387.652 92.7396 385.114 92.838 381.298 92.838H373.125C369.222 92.838 366.588 92.7396 365.318 92.5592C363.682 92.2721 362.597 91.7144 362.054 90.8778C361.687 90.2216 361.503 89.2866 361.503 88.1712V86.6785C361.666 85.6017 361.973 83.647 362.419 80.8089L362.597 79.6824L363.052 76.3196L364.863 63.1722L365.318 59.7192H352.427C352.97 56.0776 353.337 53.6499 353.513 52.5344C354.064 50.2051 354.878 48.7124 355.876 47.966C356.786 47.4001 357.967 47.031 359.507 46.8424C360.689 46.6538 362.597 46.6538 365.231 46.6538H367.138L368.95 33.2192C369.038 32.2924 369.038 31.7265 369.038 31.4477C369.038 30.9802 369.038 30.6111 368.862 30.3322C368.679 29.9549 368.407 29.4874 367.865 29.1183C367.652 28.8307 367.235 28.4955 366.645 28.0217ZM199.295 59.9111L198.848 63.2656L197.029 76.4213L196.574 79.8743L195.664 86.4029C195.576 87.3379 195.488 87.9858 195.304 88.3631C195.24 88.6345 195.123 88.8571 194.993 89.1059L194.993 89.106C194.947 89.1955 194.898 89.2885 194.849 89.3883C194.578 89.7574 194.123 90.2249 193.397 90.8811C193.037 91.2501 192.311 91.9063 191.13 92.9315H195.304C198.848 92.9315 201.203 92.8413 202.48 92.7429C204.476 92.464 205.833 91.9063 206.655 90.9713C207.381 90.0363 208.012 88.642 208.379 86.6817C208.65 85.4679 209.018 83.2288 209.465 79.8743L209.92 76.4213L211.739 63.2656C211.915 62.2404 212.011 61.5843 212.011 61.3054C212.194 60.7477 212.466 60.3704 212.641 60.19C212.825 60.0013 213.192 59.9111 213.551 59.9111C213.735 59.8127 214.006 59.8127 214.374 59.8127C214.733 59.8127 215.1 59.8127 215.547 59.9111H232.165L231.711 63.2656L229.899 76.4213L229.444 79.8743L227.624 92.9315C232.165 92.9315 235.071 92.7429 236.34 92.5624C237.889 92.1851 238.974 91.6274 239.701 90.7826C240.243 90.0363 240.698 89.011 241.065 87.5183C241.425 86.3045 241.792 83.7865 242.335 79.8743L242.79 76.4213L244.602 63.2656L245.057 59.9111C245.599 55.802 245.871 53.0954 245.871 51.8816C245.871 49.8311 245.424 48.527 244.514 47.8709C243.787 47.3132 242.422 46.9359 240.515 46.8457C239.613 46.7554 238.336 46.7554 236.707 46.7554H211.189C208.563 46.7554 206.655 46.8457 205.474 47.0343C203.837 47.2229 202.568 47.8709 201.842 48.8961C201.203 49.6425 200.66 51.045 200.301 52.9068C200.118 54.0304 199.75 56.3597 199.295 59.9111ZM93.4745 60.3564L93.4745 60.3562C93.4942 60.164 93.5126 59.985 93.5299 59.8188C93.8971 57.1122 94.1685 55.2504 94.3521 54.3154C94.8949 51.4201 95.6212 49.5583 96.5312 48.6233C97.3454 47.6883 98.6145 47.1306 100.434 46.942C101.704 46.7615 104.154 46.7615 107.786 46.7615H107.786H128.667C133.121 46.7615 135.931 46.8517 137.2 47.229C138.293 47.4095 139.107 47.9754 139.562 48.7217C140.017 49.3697 140.201 50.3949 140.201 51.7974V52.7324C140.201 53.8479 139.93 56.2756 139.379 59.8188L136.657 79.7819C136.202 83.1446 135.843 85.3837 135.571 86.5976C135.204 88.2708 134.749 89.583 134.207 90.4196C133.48 91.5351 132.299 92.1912 130.575 92.5685C129.306 92.7489 126.671 92.9376 122.585 92.9376H101.249C97.3454 92.9376 94.8071 92.7489 93.5299 92.5685C92.0772 92.2814 91.1752 91.7237 90.6245 90.9773C90.2653 90.231 89.9939 89.2058 89.9939 87.9017V86.778C90.0817 85.5642 90.3531 83.2349 90.8081 79.7819C90.9065 79.107 91.0175 78.3585 91.1395 77.5363C91.4725 75.291 91.887 72.4963 92.3486 69.1524C93.0031 64.9472 93.2947 62.1072 93.4745 60.3564ZM126.488 59.8188H110.875C109.422 59.8188 108.424 59.8188 107.969 59.9172C107.427 60.0074 106.972 60.196 106.7 60.4749C106.517 60.6635 106.333 61.2213 106.15 62.0578C106.15 62.1972 106.102 62.4889 106.041 62.8567L106.041 62.8567C106.02 62.9867 105.997 63.1261 105.974 63.2717L103.699 79.7819H122.585C122.952 79.6917 123.223 79.5933 123.399 79.4128C123.582 79.2242 123.766 78.9453 123.766 78.568C123.926 78.2471 124.013 77.709 124.154 76.8406L124.154 76.8388L124.155 76.8323C124.176 76.7046 124.198 76.5697 124.221 76.4274L126.488 59.8188ZM146.019 63.2717L146.466 59.9172C146.921 56.3658 147.288 54.0365 147.471 52.9129C147.831 51.051 148.373 49.6485 149.012 48.9022C149.738 47.877 151.008 47.229 152.644 47.0404C153.825 46.8517 155.733 46.7615 158.359 46.7615H183.878C185.506 46.7615 186.783 46.7615 187.685 46.8517C189.593 46.942 190.958 47.3192 191.684 47.877C192.594 48.5331 193.041 49.8372 193.041 51.8876C193.041 53.1015 192.77 55.8081 192.227 59.9172L191.772 63.2717L189.96 76.4274L189.505 79.8803C188.962 83.7926 188.595 86.3105 188.236 87.5244C187.869 89.0171 187.414 90.0423 186.871 90.7887C186.145 91.6335 185.059 92.1912 183.51 92.5685C182.241 92.7489 179.336 92.9376 174.794 92.9376L176.614 79.8803L177.069 76.4274L178.881 63.2717L179.336 59.9172H162.717C162.27 59.8188 161.903 59.8188 161.544 59.8188C161.177 59.8188 160.905 59.8188 160.722 59.9172C160.363 59.9172 159.995 60.0074 159.812 60.196C159.636 60.3765 159.365 60.7538 159.181 61.3115C159.181 61.5903 159.085 62.2465 158.91 63.2717L157.09 76.4274L156.635 79.8803C156.188 83.2349 155.821 85.4739 155.549 86.6878C155.182 88.648 154.552 90.0423 153.825 90.9773C153.003 91.9124 151.646 92.4701 149.651 92.7489C148.373 92.8474 146.019 92.9376 142.475 92.9376H138.3C139.481 91.9124 140.208 91.2562 140.567 90.8871C141.293 90.231 141.748 89.7635 142.02 89.3944C142.068 89.2946 142.117 89.2016 142.164 89.112C142.294 88.8632 142.41 88.6406 142.475 88.3692C142.658 87.9919 142.746 87.344 142.834 86.409L143.744 79.8803L144.199 76.4274L146.019 63.2717ZM266.091 46.7554H266.092H285.887C289.152 46.7554 291.331 46.7554 292.424 46.8457C294.42 46.9359 295.777 47.3132 296.503 47.9693C297.413 48.6172 297.78 49.9213 297.78 51.7913C297.78 52.3491 297.78 53.0052 297.685 53.7516C297.624 54.5248 297.475 55.704 297.267 57.3492L297.266 57.3535L297.266 57.3537L297.265 57.3592L297.265 57.3609C297.173 58.0861 297.07 58.9017 296.958 59.8127L296.415 63.2656C295.96 66.9974 295.601 69.4252 295.234 70.7293C294.875 72.5008 294.236 73.8049 293.51 74.5513C292.696 75.2977 291.427 75.8636 289.607 76.1424C288.425 76.3229 286.246 76.4213 283.069 76.4213H262.643C262.372 76.4213 262.101 76.6099 261.917 76.7904C261.733 76.979 261.646 77.3481 261.55 77.914C261.55 78.1929 261.462 78.8408 261.278 79.7758H281.258C284.794 79.7758 287.068 79.8743 288.066 80.0547C289.519 80.2433 290.517 80.7108 291.059 81.4572C291.786 82.2036 292.153 83.6061 292.241 85.5581C292.424 86.6817 292.424 88.642 292.424 91.3485V92.9315H258.102C254.47 92.9315 252.107 92.7429 250.838 92.5624C249.569 92.1851 248.659 91.6274 248.116 90.7826C247.749 90.0363 247.573 89.1095 247.573 87.8956V87.1492C247.661 85.9354 247.932 83.5076 248.387 79.7758L251.109 59.8127L252.203 53.0954C252.658 50.8563 253.288 49.3636 254.111 48.527C255.013 47.6822 256.29 47.1245 257.918 46.9359C259.195 46.7554 261.917 46.7554 266.091 46.7554ZM283.979 59.8127H266.275C265.453 59.8127 264.91 59.8127 264.727 59.9111C264.566 59.9938 264.398 60.1593 264.23 60.3253L264.184 60.3704L264.148 60.447C264.069 60.6146 263.977 60.8104 263.825 61.2152C263.825 61.494 263.729 62.142 263.553 63.2656H282.343C282.886 63.1754 283.253 62.9868 283.437 62.7981C283.612 62.6095 283.708 62.3306 283.708 61.9615C283.796 61.5843 283.884 60.9363 283.979 59.8127ZM315.2 46.7554C311.935 46.7554 309.66 46.8457 308.391 47.0343C306.755 47.2229 305.573 47.6822 304.759 48.4286C303.945 49.175 303.306 50.5775 302.852 52.5377C302.668 53.7516 302.309 56.1793 301.766 59.9111C301.495 62.142 301.04 65.4145 300.401 69.8927C299.858 74.3709 299.403 77.6352 299.036 79.8743C298.493 83.5076 298.222 85.9354 298.134 87.2395V87.8956C298.134 89.1997 298.406 90.2249 298.765 90.9713C299.315 91.7176 300.217 92.2754 301.495 92.5624C302.764 92.7429 305.573 92.9315 309.932 92.9315H330.27C333.997 92.9315 336.448 92.8413 337.629 92.7429C339.441 92.5624 340.806 92.0949 341.62 91.4388C342.53 90.6924 343.161 89.3883 343.528 87.707C343.887 86.4029 344.254 83.7865 344.797 79.8743H311.935L314.202 63.2656C314.386 62.3306 314.473 61.7729 314.473 61.3956C314.657 60.8379 314.841 60.4688 315.016 60.2802C315.2 60.0915 315.471 60.0013 315.838 60.0013C316.014 59.9111 316.469 59.8127 317.02 59.8127C317.181 59.8127 317.361 59.8325 317.56 59.8543C317.805 59.8812 318.077 59.9111 318.377 59.9111H334.628C335.793 59.8778 336.825 59.8455 337.725 59.8173L337.726 59.8173C339.49 59.762 340.751 59.7225 341.533 59.7225C343.712 59.5338 345.34 58.9761 346.25 58.1313C347.064 57.385 347.703 56.0809 348.07 54.3093C348.429 53.0052 348.796 50.4872 349.339 46.7554H315.2Z"
      fill="#3463FC"
    />
    <path
      d="M263.984 112.7L263.569 115.734L261.933 127.627L261.518 130.752L260.704 136.649C260.616 137.494 260.536 138.084 260.376 138.421C260.289 138.757 260.129 139.011 259.961 139.347C259.714 139.684 259.307 140.11 258.652 140.701C258.317 141.037 257.663 141.628 256.601 142.554H260.376C263.569 142.554 265.708 142.472 266.858 142.382C268.662 142.136 269.891 141.628 270.633 140.783C271.288 139.938 271.863 138.675 272.19 136.903C272.437 135.804 272.765 133.787 273.172 130.752L273.587 127.627L275.223 115.734C275.391 114.808 275.471 114.217 275.471 113.971C275.638 113.463 275.886 113.126 276.045 112.954C276.213 112.79 276.54 112.7 276.867 112.7C277.035 112.618 277.282 112.618 277.61 112.618C277.937 112.618 278.264 112.618 278.671 112.7H293.694L293.278 115.734L291.642 127.627L291.227 130.752L289.591 142.554C293.694 142.554 296.32 142.382 297.469 142.218C298.866 141.882 299.848 141.373 300.502 140.619C300.997 139.938 301.404 139.011 301.731 137.666C302.067 136.567 302.394 134.287 302.881 130.752L303.296 127.627L304.932 115.734L305.347 112.7C305.842 108.993 306.082 106.548 306.082 105.449C306.082 103.596 305.675 102.415 304.852 101.824C304.198 101.316 302.969 100.979 301.245 100.897C300.422 100.815 299.273 100.815 297.796 100.815H274.736C272.358 100.815 270.633 100.897 269.564 101.061C268.087 101.234 266.938 101.824 266.283 102.751C265.708 103.424 265.214 104.695 264.886 106.376C264.719 107.393 264.391 109.501 263.984 112.7Z"
      fill="#8500BB"
    />
    <path
      d="M310.865 112.618C310.697 114.135 310.45 116.834 309.795 121.049C309.221 125.183 308.726 128.39 308.399 130.662C307.991 133.787 307.744 135.894 307.664 136.985V138.002C307.664 139.183 307.912 140.11 308.239 140.783C308.726 141.455 309.548 141.964 310.865 142.218C312.014 142.382 314.313 142.554 317.841 142.554H337.126C340.822 142.554 343.2 142.382 344.35 142.218C345.906 141.882 346.976 141.291 347.63 140.274C348.125 139.52 348.532 138.339 348.86 136.821C349.107 135.722 349.434 133.696 349.849 130.662L352.308 112.618C352.803 109.411 353.05 107.221 353.05 106.212V105.367C353.05 104.104 352.883 103.169 352.475 102.579C352.06 101.906 351.326 101.406 350.336 101.234C349.187 100.897 346.649 100.815 342.626 100.815H323.748C320.467 100.815 318.248 100.815 317.099 100.979C315.463 101.152 314.313 101.652 313.571 102.497C312.749 103.342 312.094 105.031 311.599 107.639C311.44 108.484 311.192 110.174 310.865 112.618ZM326.542 112.618H340.654L338.603 127.627C338.435 128.644 338.355 129.226 338.196 129.571C338.196 129.907 338.028 130.161 337.86 130.325C337.701 130.498 337.453 130.58 337.126 130.662H320.052L322.104 115.734C322.192 115.234 322.271 114.808 322.271 114.644C322.439 113.881 322.599 113.381 322.766 113.208C323.006 112.954 323.421 112.79 323.916 112.7C324.323 112.618 325.225 112.618 326.542 112.618Z"
      fill="#8500BB"
    />
    <path
      d="M356.16 100.735C356.982 101.916 357.557 102.679 357.804 103.179C358.211 103.688 358.459 104.196 358.538 104.532C358.706 104.869 358.706 105.205 358.706 105.631C358.706 105.795 358.706 106.222 358.626 106.894L357.15 117.606L355.338 130.671C355.01 133.624 354.763 135.65 354.683 136.659C354.683 137.085 354.603 137.504 354.603 137.93C354.603 139.103 354.763 139.948 355.098 140.628C355.585 141.465 356.655 141.973 358.211 142.31C359.361 142.482 361.739 142.564 365.355 142.564H383.49C386.611 142.564 388.663 142.482 389.645 142.392C391.864 142.228 393.34 141.719 394.162 140.874C394.897 140.038 395.471 138.767 395.799 136.995C396.046 135.904 396.373 133.796 396.788 130.671L399.247 112.545C399.742 108.83 399.989 106.386 399.989 105.295C399.989 103.605 399.654 102.506 398.92 101.916C398.345 101.325 397.275 100.989 395.799 100.907C394.737 100.735 392.518 100.735 389.15 100.735L386.859 117.606L385.047 130.671C383.985 130.671 383.243 130.589 382.916 130.589C382.588 130.417 382.341 130.335 382.173 130.171C382.093 129.917 382.093 129.581 382.093 129.154C382.093 128.818 382.173 128.309 382.261 127.473L384.312 112.455C384.64 109.929 384.799 108.24 384.887 107.313C384.967 106.64 384.967 106.05 384.967 105.541C384.967 104.024 384.799 103.015 384.312 102.424C383.818 101.58 382.748 101.071 381.192 100.907C380.122 100.735 377.831 100.735 374.295 100.735L372.004 117.015L370.192 130.671C369.043 130.671 368.308 130.589 367.981 130.589C367.654 130.417 367.406 130.253 367.319 129.917C367.319 129.663 367.239 129.326 367.239 128.9C367.319 128.654 367.406 128.227 367.406 127.637L369.458 112.545C370.033 108.83 370.28 106.386 370.28 105.205C370.28 103.605 369.945 102.506 369.21 101.916C368.716 101.416 367.814 101.071 366.664 100.907C365.842 100.735 364.693 100.735 363.136 100.735H356.16Z"
      fill="#8500BB"
    />
    <path
      d="M34.0677 90.4335C34.2795 91.2241 34.246 92.0615 33.9711 92.8398C33.6963 93.618 33.1927 94.3022 32.5238 94.8057C31.855 95.3092 31.051 95.6095 30.2136 95.6685C29.3761 95.7276 28.5429 95.5428 27.8191 95.1375C27.0953 94.7322 26.5136 94.1246 26.1475 93.3916C25.7813 92.6586 25.6472 91.833 25.7621 91.0193C25.877 90.2056 26.2357 89.4403 26.7929 88.8202C27.3501 88.2001 28.0808 87.753 28.8925 87.5355C29.981 87.2439 31.1377 87.3853 32.1082 87.9288C33.0788 88.4723 33.7836 89.3732 34.0677 90.4335ZM13.3148 81.4396C16.2327 77.5679 20.401 74.7671 25.1316 73.4996C29.8622 72.232 34.8723 72.5734 39.3352 74.4675C39.6782 74.6697 40.0622 74.7977 40.4618 74.8431C40.8613 74.8885 41.2673 74.8503 41.6528 74.7308C42.0382 74.6114 42.3944 74.4135 42.6977 74.1504C43.0009 73.8873 43.2443 73.5648 43.4117 73.2045C43.5791 72.8442 43.6667 72.4542 43.6687 72.0603C43.6706 71.6665 43.5869 71.2779 43.4231 70.9203C43.2593 70.5626 43.019 70.2441 42.7184 69.9858C42.4177 69.7276 42.0635 69.5355 41.6792 69.4223C36.0369 67.0288 29.7032 66.5977 23.7229 68.2001C17.7426 69.8025 12.473 73.3427 8.78343 78.2366C8.45994 78.8299 8.36469 79.5146 8.51515 80.1649C8.66562 80.8152 9.05171 81.3876 9.60257 81.777C10.1534 82.1663 10.8321 82.3466 11.514 82.2846C12.1959 82.2226 12.8353 81.9226 13.3148 81.4396ZM38.6225 82.8081C38.7758 82.4729 38.8604 82.1122 38.8713 81.7468C38.8822 81.3813 38.8193 81.0183 38.6861 80.6786C38.553 80.3388 38.3522 80.029 38.0953 79.7667C37.8384 79.5045 37.5304 79.2951 37.189 79.1505C33.8098 77.6788 29.9986 77.4014 26.4013 78.3653C22.8041 79.3291 19.6422 81.475 17.4516 84.4392C17.1145 85.0354 17.0104 85.7286 17.1587 86.3883C17.3069 87.0481 17.6975 87.6291 18.2568 88.0219C18.8161 88.4147 19.5056 88.5923 20.1957 88.5213C20.8858 88.4503 21.5289 88.1357 22.0041 87.6365C23.4415 85.7167 25.5019 84.3276 27.8422 83.7005C30.1825 83.0734 32.6615 83.2462 34.8662 84.1901C35.2072 84.3356 35.5751 84.4135 35.9488 84.4194C36.3225 84.4252 36.6947 84.3589 37.0441 84.2243C37.3935 84.0896 37.7131 83.8893 37.9848 83.6347C38.2564 83.3801 38.4747 83.0763 38.6271 82.7406L38.6225 82.8081Z"
      fill="#8500BB"
    />
    <path
      fill-rule="evenodd"
      clip-rule="evenodd"
      d="M65.1169 49.4493C71.3953 43.1709 80.7731 35.7487 87.136 30.9122C90.3735 31.2543 93.6596 30.5697 96.5145 28.9215C100.074 26.8662 102.672 23.4809 103.736 19.5104C104.8 15.5399 104.243 11.3094 102.188 7.74952C100.132 4.18965 96.747 1.59205 92.7764 0.528151C88.8059 -0.535745 84.5754 0.0212233 81.0155 2.07651C77.4557 4.1318 74.8581 7.51705 73.7942 11.4876C73.3368 13.1945 73.179 14.9495 73.311 16.6786C68.4799 23.0437 60.9528 32.5747 54.5975 38.9299C49.4881 44.0393 42.326 49.9062 36.4067 54.5206C35.899 54.3524 35.3852 54.1985 34.8657 54.0594C27.7703 52.1581 20.2102 53.1535 13.8486 56.8263C7.48705 60.4992 2.84504 66.5487 0.943826 73.6442C-0.95739 80.7396 0.0379186 88.2997 3.71078 94.6613C7.38365 101.023 13.4332 105.665 20.5287 107.566C27.6241 109.467 35.1842 108.472 41.5458 104.799C43.1399 103.879 44.6261 102.809 45.9883 101.611C50.8936 103.835 55.8991 106.276 59.9786 108.631C69.3453 114.039 81.5416 123.45 87.1961 127.933C87.1825 128.995 87.2451 130.058 87.3843 131.115C87.765 134.005 88.7113 136.792 90.1692 139.317C91.6265 141.842 93.567 144.055 95.8798 145.83C98.1926 147.605 100.832 148.906 103.648 149.661C106.465 150.416 109.402 150.608 112.292 150.227C115.183 149.847 117.97 148.901 120.495 147.443C123.02 145.985 125.233 144.044 127.008 141.731C128.783 139.418 130.085 136.778 130.839 133.962C131.594 131.146 131.786 128.209 131.406 125.319C131.025 122.428 130.079 119.641 128.621 117.117C127.164 114.592 125.223 112.379 122.91 110.604C120.598 108.829 117.958 107.527 115.142 106.773C112.326 106.018 109.388 105.826 106.498 106.206C103.607 106.587 100.82 107.533 98.295 108.991C97.9936 109.165 97.6967 109.346 97.4044 109.533C90.5639 106.819 76.6294 101.066 67.4169 95.7473C63.422 93.4409 58.9124 90.4063 54.6151 87.3363C56.2891 80.4297 55.2454 73.1334 51.6836 66.9642C51.4553 66.5688 51.2179 66.18 50.9715 65.7981C55.3266 60.2846 60.5273 54.0389 65.1169 49.4493ZM27.6397 103.038C39.9573 103.038 49.9426 93.0531 49.9426 80.7356C49.9426 68.418 39.9573 58.4327 27.6397 58.4327C15.3222 58.4327 5.33691 68.418 5.33691 80.7356C5.33691 93.0531 15.3222 103.038 27.6397 103.038Z"
      fill="#8500BB"
    />
    <path
      fill-rule="evenodd"
      clip-rule="evenodd"
      d="M85.4444 24.8547L87.7505 25.0985C89.767 25.3116 91.8136 24.8851 93.5914 23.8586L96.4303 28.7756L93.5914 23.8586C95.8086 22.5786 97.4264 20.4702 98.089 17.9973L103.492 19.445L98.089 17.9973C98.7516 15.5245 98.4047 12.8897 97.1247 10.6726C95.8446 8.45543 93.7362 6.83762 91.2634 6.17502C88.7905 5.51242 86.1557 5.8593 83.9386 7.13936C81.7215 8.4194 80.1037 10.5278 79.4411 13.0006C79.1562 14.0639 79.0579 15.1566 79.1401 16.2335L79.309 18.4457L77.9677 20.213C73.1297 26.5871 65.3943 36.4007 58.7313 43.0637C54.1534 47.6416 48.1097 52.7066 42.7399 56.9754C44.4598 58.0707 46.0528 59.3477 47.4918 60.7792C51.6584 55.5592 56.545 49.7536 60.9831 45.3155C67.567 38.7316 77.2248 31.1027 83.5983 26.258L85.4444 24.8547ZM51.2655 65.4264C51.1671 65.5507 51.0691 65.6746 50.9715 65.7981C51.2179 66.18 51.4553 66.5688 51.6836 66.9642C54.1225 71.1885 55.3808 75.9413 55.3943 80.7356C55.4005 82.9429 55.1429 85.159 54.6151 87.3363C54.7271 87.4164 54.8393 87.4964 54.9516 87.5763C59.1472 90.564 63.5261 93.501 67.4169 95.7473C76.6294 101.066 90.5639 106.819 97.4044 109.533C97.6967 109.346 97.9936 109.165 98.295 108.991C100.82 107.533 103.607 106.587 106.498 106.206C109.388 105.826 112.326 106.018 115.142 106.773L113.629 112.42C111.554 111.864 109.39 111.722 107.261 112.002C105.132 112.283 103.078 112.98 101.218 114.054C100.996 114.182 100.777 114.315 100.562 114.453L98.037 116.074L95.2483 114.967C88.4277 112.261 74.1221 106.369 64.4939 100.81C60.7895 98.6715 56.7377 95.9844 52.8576 93.2563C52.1646 94.6494 51.3595 95.977 50.4542 97.2272C54.7868 99.2353 59.1733 101.416 62.9016 103.568C72.6879 109.218 85.1934 118.884 90.8283 123.352L93.0784 125.137L93.0417 128.008C93.0317 128.79 93.0778 129.573 93.1803 130.352C93.4607 132.481 94.1578 134.534 95.2317 136.393L95.2323 136.395C96.3058 138.254 97.7352 139.884 99.4388 141.192C101.142 142.499 103.087 143.458 105.162 144.014C107.236 144.57 109.4 144.712 111.529 144.431L112.292 150.227C109.402 150.608 106.465 150.416 103.648 149.661C100.832 148.906 98.1926 147.605 95.8798 145.83C93.567 144.055 91.6265 141.842 90.1692 139.317C88.7113 136.792 87.765 134.005 87.3843 131.115C87.2451 130.058 87.1825 128.995 87.1961 127.933C81.5416 123.45 69.3453 114.039 59.9786 108.631C55.9975 106.332 51.1346 103.953 46.3434 101.772C46.225 101.719 46.1066 101.665 45.9883 101.611C44.6261 102.809 43.1399 103.879 41.5458 104.799C37.2819 107.261 32.4796 108.52 27.6397 108.51C25.2586 108.505 22.8684 108.193 20.5287 107.566C13.4332 105.665 7.38365 101.023 3.71078 94.6613C1.2457 90.3916 -0.0132566 85.5821 0.000105254 80.7356C0.00665199 78.3609 0.31863 75.9774 0.943826 73.6442C2.84504 66.5487 7.48705 60.4992 13.8486 56.8263C18.0788 54.3841 22.8388 53.1257 27.6397 53.1156C30.0589 53.1105 32.4884 53.4223 34.8657 54.0594C35.3852 54.1985 35.899 54.3524 36.4067 54.5206C36.5673 54.3954 36.7288 54.2693 36.8912 54.1423C42.7131 49.5884 49.6267 43.9007 54.5975 38.9299C60.9528 32.5747 68.4799 23.0437 73.311 16.6786C73.179 14.9495 73.3368 13.1945 73.7942 11.4876C74.8581 7.51705 77.4557 4.1318 81.0155 2.07651C84.5754 0.0212233 88.8059 -0.535745 92.7764 0.528151C96.747 1.59205 100.132 4.18965 102.188 7.74952C104.243 11.3094 104.8 15.5399 103.736 19.5104C102.672 23.4809 100.074 26.8662 96.5145 28.9215C93.6596 30.5697 90.3735 31.2543 87.136 30.9122C80.7731 35.7487 71.3953 43.1709 65.1169 49.4493C60.6302 53.936 55.5594 60.0055 51.2655 65.4264ZM44.9964 94.7426C46.206 93.2456 47.2259 91.5889 48.0195 89.8091C48.555 88.6082 48.9874 87.3512 49.3056 86.0494C49.7219 84.3464 49.9426 82.5668 49.9426 80.7356C49.9426 76.7066 48.8743 72.9272 47.0055 69.6651C46.8229 69.3465 46.6328 69.0328 46.4352 68.7243C45.6735 67.5349 44.802 66.4225 43.835 65.4015C42.0967 63.5662 40.0498 62.0262 37.7774 60.8646C36.801 60.3654 35.7829 59.9362 34.7299 59.5833C34.3205 59.4462 33.9059 59.3206 33.4864 59.2069C31.6231 58.7021 29.6629 58.4327 27.6397 58.4327C23.5807 58.4327 19.7748 59.5171 16.4961 61.4119C11.4215 64.3447 7.60974 69.219 6.07619 75.0194C5.59387 76.8437 5.33691 78.7596 5.33691 80.7356C5.33691 84.8328 6.44175 88.672 8.36975 91.9714C11.3107 97.0044 16.167 100.782 21.9389 102.303C23.7586 102.783 25.6693 103.038 27.6397 103.038C31.7068 103.038 35.5196 101.95 38.8027 100.048C40.0741 99.3115 41.2661 98.453 42.3626 97.4886C43.3163 96.6499 44.1977 95.731 44.9964 94.7426ZM112.292 150.227C115.183 149.847 117.97 148.901 120.495 147.443C123.02 145.985 125.233 144.044 127.008 141.731C128.783 139.418 130.085 136.778 130.839 133.962C131.594 131.146 131.786 128.209 131.406 125.319C131.025 122.428 130.079 119.641 128.621 117.117C127.164 114.592 125.223 112.379 122.91 110.604C120.598 108.829 117.958 107.527 115.142 106.773L113.629 112.42C115.703 112.976 117.648 113.935 119.351 115.242C121.055 116.549 122.484 118.18 123.558 120.039L123.558 120.04C124.632 121.9 125.329 123.953 125.61 126.082C125.89 128.211 125.748 130.375 125.193 132.449C124.637 134.524 123.678 136.468 122.37 138.172C121.063 139.876 119.432 141.306 117.572 142.38C115.712 143.454 113.659 144.151 111.529 144.431L112.292 150.227Z"
      fill="#8500BB"
    />
    <path
      d="M32.0409 146.111C29.9142 142.427 29.3378 138.05 30.4387 133.941C31.5396 129.832 34.2275 126.329 37.9111 124.203C41.5948 122.076 45.9724 121.5 50.081 122.601C54.1895 123.701 57.6925 126.389 59.8192 130.073C61.946 133.757 62.5223 138.134 61.4214 142.243C60.3205 146.351 57.6326 149.854 53.949 151.981C50.2653 154.108 45.8877 154.684 41.7792 153.583C37.6706 152.482 34.1676 149.794 32.0409 146.111Z"
      fill="#8500BB"
    />
    <path
      d="M9.45999 26.8049C7.92513 24.1464 7.5092 20.9871 8.30371 18.022C9.09821 15.0569 11.0381 12.5288 13.6965 10.994C16.355 9.45911 19.5143 9.04317 22.4794 9.83768C25.4445 10.6322 27.9726 12.572 29.5074 15.2305C31.0423 17.8889 31.4582 21.0482 30.6637 24.0134C29.8692 26.9785 27.9294 29.5066 25.2709 31.0414C22.6125 32.5763 19.4532 32.9922 16.488 32.1977C13.5229 31.4032 10.9949 29.4633 9.45999 26.8049Z"
      fill="#8500BB"
    />
  </svg>
);
const LoginCodeIconDown = () => (
  <svg
    width="400"
    height="160"
    viewBox="0 0 400 160"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      fill-rule="evenodd"
      clip-rule="evenodd"
      d="M366.645 28.0217C366.462 27.874 366.261 27.7128 366.045 27.5354H374.402C375.855 27.5354 376.94 27.6256 377.667 27.8143C379.119 28.0029 380.205 28.4622 380.756 29.1183C381.298 29.8647 381.57 30.9802 381.57 32.6615C381.57 33.8754 381.386 36.4917 380.843 40.5926L380.029 46.6538H400C399.458 50.7629 399.003 53.371 398.635 54.6833C398.189 56.5451 397.366 57.759 396.281 58.5053C395.554 58.9728 394.557 59.3419 393.104 59.5306C391.827 59.6208 389.289 59.7192 385.289 59.7192H378.209L377.754 63.1722L375.943 76.3196L375.488 79.6824H395.459L395.442 79.8002C394.909 83.6551 394.553 86.2241 394.19 87.5151C393.83 89.1062 393.192 90.3201 392.465 91.0664C391.739 91.8128 390.558 92.2721 388.921 92.5592C387.652 92.7396 385.114 92.838 381.298 92.838H373.125C369.222 92.838 366.588 92.7396 365.318 92.5592C363.682 92.2721 362.596 91.7144 362.054 90.8778C361.687 90.2216 361.503 89.2866 361.503 88.1712V86.6785C361.666 85.6017 361.973 83.647 362.419 80.8089L362.596 79.6824L363.051 76.3196L364.863 63.1722L365.318 59.7192H352.427C352.97 56.0776 353.337 53.6499 353.513 52.5344C354.064 50.2051 354.878 48.7124 355.876 47.966C356.786 47.4001 357.967 47.031 359.507 46.8424C360.689 46.6538 362.596 46.6538 365.231 46.6538H367.138L368.95 33.2192C369.038 32.2924 369.038 31.7265 369.038 31.4477C369.038 30.9802 369.038 30.6111 368.862 30.3322C368.679 29.9549 368.407 29.4874 367.865 29.1183C367.652 28.8307 367.235 28.4955 366.645 28.0217ZM199.295 59.9111L198.848 63.2656L197.029 76.4213L196.574 79.8743L195.664 86.4029C195.576 87.3379 195.488 87.9858 195.304 88.3631C195.24 88.6345 195.124 88.8571 194.993 89.1059L194.993 89.106C194.947 89.1955 194.898 89.2885 194.849 89.3883C194.578 89.7574 194.123 90.2249 193.397 90.8811C193.037 91.2501 192.311 91.9063 191.13 92.9315H195.304C198.848 92.9315 201.203 92.8413 202.48 92.7429C204.476 92.464 205.833 91.9063 206.655 90.9713C207.381 90.0363 208.012 88.642 208.379 86.6817C208.65 85.4679 209.018 83.2288 209.465 79.8743L209.92 76.4213L211.739 63.2656C211.915 62.2404 212.011 61.5843 212.011 61.3054C212.194 60.7477 212.466 60.3704 212.641 60.19C212.825 60.0013 213.192 59.9111 213.551 59.9111C213.735 59.8127 214.006 59.8127 214.374 59.8127C214.733 59.8127 215.1 59.8127 215.547 59.9111H232.166L231.711 63.2656L229.899 76.4213L229.444 79.8743L227.624 92.9315C232.166 92.9315 235.071 92.7429 236.34 92.5624C237.889 92.1851 238.974 91.6274 239.701 90.7826C240.243 90.0363 240.698 89.011 241.066 87.5183C241.425 86.3045 241.792 83.7865 242.335 79.8743L242.79 76.4213L244.602 63.2656L245.057 59.9111C245.599 55.802 245.871 53.0954 245.871 51.8816C245.871 49.8311 245.424 48.527 244.514 47.8709C243.787 47.3132 242.422 46.9359 240.515 46.8457C239.613 46.7554 238.336 46.7554 236.707 46.7554H211.189C208.563 46.7554 206.655 46.8457 205.474 47.0343C203.837 47.2229 202.568 47.8709 201.842 48.8961C201.203 49.6425 200.66 51.045 200.301 52.9068C200.118 54.0304 199.75 56.3597 199.295 59.9111ZM93.4745 60.3564L93.4745 60.3562C93.4942 60.164 93.5126 59.985 93.5299 59.8188C93.8971 57.1122 94.1685 55.2504 94.3521 54.3154C94.8949 51.4201 95.6212 49.5583 96.5312 48.6233C97.3454 47.6883 98.6145 47.1306 100.434 46.942C101.704 46.7615 104.154 46.7615 107.786 46.7615H107.786H128.667C133.121 46.7615 135.931 46.8517 137.2 47.229C138.293 47.4095 139.108 47.9754 139.562 48.7217C140.017 49.3697 140.201 50.3949 140.201 51.7974V52.7324C140.201 53.8479 139.93 56.2756 139.379 59.8188L136.657 79.7819C136.202 83.1446 135.843 85.3837 135.571 86.5976C135.204 88.2708 134.749 89.583 134.207 90.4196C133.48 91.5351 132.299 92.1912 130.575 92.5685C129.306 92.7489 126.671 92.9376 122.585 92.9376H101.249C97.3454 92.9376 94.8071 92.7489 93.5299 92.5685C92.0772 92.2814 91.1752 91.7237 90.6245 90.9773C90.2653 90.231 89.9939 89.2058 89.9939 87.9017V86.778C90.0817 85.5642 90.3531 83.2349 90.8081 79.7819C90.9065 79.107 91.0175 78.3585 91.1395 77.5363C91.4725 75.291 91.887 72.4963 92.3486 69.1524C93.0031 64.9472 93.2947 62.1072 93.4745 60.3564ZM126.488 59.8188H110.875C109.422 59.8188 108.424 59.8188 107.969 59.9172C107.427 60.0074 106.972 60.196 106.7 60.4749C106.517 60.6635 106.333 61.2213 106.15 62.0578C106.15 62.1972 106.102 62.4889 106.041 62.8567L106.041 62.8567C106.02 62.9867 105.997 63.1261 105.974 63.2717L103.699 79.7819H122.585C122.952 79.6917 123.223 79.5933 123.399 79.4128C123.582 79.2242 123.766 78.9453 123.766 78.568C123.926 78.2471 124.013 77.709 124.154 76.8406L124.154 76.8388L124.155 76.8323C124.176 76.7046 124.198 76.5697 124.221 76.4274L126.488 59.8188ZM146.019 63.2717L146.466 59.9172C146.921 56.3658 147.288 54.0365 147.472 52.9129C147.831 51.051 148.373 49.6485 149.012 48.9022C149.738 47.877 151.008 47.229 152.644 47.0404C153.825 46.8517 155.733 46.7615 158.359 46.7615H183.878C185.506 46.7615 186.783 46.7615 187.685 46.8517C189.593 46.942 190.958 47.3192 191.684 47.877C192.594 48.5331 193.041 49.8372 193.041 51.8876C193.041 53.1015 192.77 55.8081 192.227 59.9172L191.772 63.2717L189.96 76.4274L189.505 79.8803C188.962 83.7926 188.595 86.3105 188.236 87.5244C187.869 89.0171 187.414 90.0423 186.871 90.7887C186.145 91.6335 185.059 92.1912 183.51 92.5685C182.241 92.7489 179.336 92.9376 174.794 92.9376L176.614 79.8803L177.069 76.4274L178.881 63.2717L179.336 59.9172H162.717C162.27 59.8188 161.903 59.8188 161.544 59.8188C161.177 59.8188 160.905 59.8188 160.722 59.9172C160.363 59.9172 159.995 60.0074 159.812 60.196C159.636 60.3765 159.365 60.7538 159.181 61.3115C159.181 61.5903 159.085 62.2465 158.91 63.2717L157.09 76.4274L156.635 79.8803C156.188 83.2349 155.821 85.4739 155.549 86.6878C155.182 88.648 154.552 90.0423 153.825 90.9773C153.003 91.9124 151.646 92.4701 149.651 92.7489C148.373 92.8474 146.019 92.9376 142.475 92.9376H138.3C139.481 91.9124 140.208 91.2562 140.567 90.8871C141.293 90.231 141.748 89.7635 142.02 89.3944C142.068 89.2946 142.117 89.2016 142.164 89.112C142.294 88.8632 142.41 88.6406 142.475 88.3692C142.658 87.9919 142.746 87.344 142.834 86.409L143.744 79.8803L144.199 76.4274L146.019 63.2717ZM266.092 46.7554H266.092H285.887C289.152 46.7554 291.331 46.7554 292.424 46.8457C294.42 46.9359 295.777 47.3132 296.503 47.9693C297.413 48.6172 297.78 49.9213 297.78 51.7913C297.78 52.3491 297.78 53.0052 297.685 53.7516C297.624 54.5248 297.475 55.704 297.267 57.3492L297.266 57.3535L297.266 57.3537L297.265 57.3592L297.265 57.3609C297.173 58.0861 297.07 58.9017 296.958 59.8127L296.415 63.2656C295.961 66.9974 295.601 69.4252 295.234 70.7293C294.875 72.5008 294.236 73.8049 293.51 74.5513C292.696 75.2977 291.427 75.8636 289.607 76.1424C288.425 76.3229 286.246 76.4213 283.069 76.4213H262.643C262.372 76.4213 262.101 76.6099 261.917 76.7904C261.733 76.979 261.646 77.3481 261.55 77.914C261.55 78.1929 261.462 78.8408 261.278 79.7758H281.258C284.794 79.7758 287.068 79.8743 288.066 80.0547C289.519 80.2433 290.517 80.7108 291.06 81.4572C291.786 82.2036 292.153 83.6061 292.241 85.5581C292.424 86.6817 292.424 88.642 292.424 91.3485V92.9315H258.102C254.47 92.9315 252.107 92.7429 250.838 92.5624C249.569 92.1851 248.659 91.6274 248.116 90.7826C247.749 90.0363 247.573 89.1095 247.573 87.8956V87.1492C247.661 85.9354 247.932 83.5076 248.387 79.7758L251.109 59.8127L252.203 53.0954C252.658 50.8563 253.288 49.3636 254.111 48.527C255.013 47.6822 256.29 47.1245 257.918 46.9359C259.195 46.7554 261.917 46.7554 266.092 46.7554ZM283.979 59.8127H266.275C265.453 59.8127 264.91 59.8127 264.727 59.9111C264.566 59.9938 264.398 60.1593 264.23 60.3253L264.184 60.3704L264.148 60.447C264.069 60.6146 263.977 60.8104 263.825 61.2152C263.825 61.494 263.729 62.142 263.553 63.2656H282.343C282.886 63.1754 283.253 62.9868 283.437 62.7981C283.612 62.6095 283.708 62.3306 283.708 61.9615C283.796 61.5843 283.884 60.9363 283.979 59.8127ZM315.2 46.7554C311.935 46.7554 309.66 46.8457 308.391 47.0343C306.755 47.2229 305.573 47.6822 304.759 48.4286C303.945 49.175 303.307 50.5775 302.852 52.5377C302.668 53.7516 302.309 56.1793 301.766 59.9111C301.495 62.142 301.04 65.4145 300.401 69.8927C299.858 74.3709 299.403 77.6352 299.036 79.8743C298.493 83.5076 298.222 85.9354 298.134 87.2395V87.8956C298.134 89.1997 298.406 90.2249 298.765 90.9713C299.316 91.7176 300.217 92.2754 301.495 92.5624C302.764 92.7429 305.573 92.9315 309.932 92.9315H330.27C333.998 92.9315 336.448 92.8413 337.629 92.7429C339.441 92.5624 340.806 92.0949 341.62 91.4388C342.53 90.6924 343.161 89.3883 343.528 87.707C343.887 86.4029 344.254 83.7865 344.797 79.8743H311.935L314.202 63.2656C314.386 62.3306 314.473 61.7729 314.473 61.3956C314.657 60.8379 314.841 60.4688 315.016 60.2802C315.2 60.0915 315.471 60.0013 315.838 60.0013C316.014 59.9111 316.469 59.8127 317.02 59.8127C317.181 59.8127 317.361 59.8325 317.56 59.8543C317.805 59.8812 318.077 59.9111 318.377 59.9111H334.628C335.794 59.8778 336.825 59.8455 337.725 59.8173L337.726 59.8173C339.49 59.762 340.751 59.7225 341.533 59.7225C343.712 59.5338 345.34 58.9761 346.25 58.1313C347.064 57.385 347.703 56.0809 348.07 54.3093C348.429 53.0052 348.796 50.4872 349.339 46.7554H315.2Z"
      fill="white"
    />
    <path
      d="M263.984 112.7L263.569 115.734L261.933 127.627L261.518 130.752L260.704 136.649C260.616 137.494 260.536 138.084 260.376 138.421C260.289 138.757 260.129 139.011 259.961 139.347C259.714 139.684 259.307 140.11 258.652 140.701C258.317 141.037 257.663 141.628 256.601 142.554H260.376C263.569 142.554 265.708 142.472 266.858 142.382C268.662 142.136 269.891 141.628 270.633 140.783C271.288 139.938 271.863 138.675 272.19 136.903C272.437 135.804 272.765 133.787 273.172 130.752L273.587 127.627L275.223 115.734C275.391 114.808 275.471 114.217 275.471 113.971C275.638 113.463 275.886 113.126 276.045 112.954C276.213 112.79 276.54 112.7 276.867 112.7C277.035 112.618 277.282 112.618 277.61 112.618C277.937 112.618 278.264 112.618 278.671 112.7H293.694L293.278 115.734L291.642 127.627L291.227 130.752L289.591 142.554C293.694 142.554 296.32 142.382 297.469 142.218C298.866 141.882 299.848 141.373 300.502 140.619C300.997 139.938 301.404 139.011 301.731 137.666C302.067 136.567 302.394 134.287 302.881 130.752L303.296 127.627L304.932 115.734L305.347 112.7C305.842 108.993 306.082 106.548 306.082 105.449C306.082 103.596 305.675 102.415 304.852 101.824C304.198 101.316 302.969 100.979 301.245 100.897C300.422 100.815 299.273 100.815 297.796 100.815H274.736C272.358 100.815 270.633 100.897 269.564 101.061C268.087 101.234 266.938 101.824 266.283 102.751C265.708 103.424 265.214 104.695 264.886 106.376C264.719 107.393 264.391 109.501 263.984 112.7Z"
      fill="white"
    />
    <path
      d="M310.865 112.618C310.697 114.135 310.45 116.834 309.796 121.049C309.221 125.183 308.726 128.39 308.399 130.662C307.992 133.787 307.744 135.894 307.664 136.985V138.002C307.664 139.183 307.912 140.11 308.239 140.783C308.726 141.455 309.548 141.964 310.865 142.218C312.015 142.382 314.313 142.554 317.841 142.554H337.126C340.822 142.554 343.2 142.382 344.35 142.218C345.906 141.882 346.976 141.291 347.63 140.274C348.125 139.52 348.532 138.339 348.86 136.821C349.107 135.722 349.434 133.696 349.849 130.662L352.308 112.618C352.803 109.411 353.05 107.221 353.05 106.212V105.367C353.05 104.104 352.883 103.169 352.476 102.579C352.061 101.906 351.326 101.406 350.336 101.234C349.187 100.897 346.649 100.815 342.626 100.815H323.748C320.468 100.815 318.249 100.815 317.099 100.979C315.463 101.152 314.313 101.652 313.571 102.497C312.749 103.342 312.094 105.031 311.599 107.639C311.44 108.484 311.192 110.174 310.865 112.618ZM326.542 112.618H340.654L338.603 127.627C338.435 128.644 338.355 129.226 338.196 129.571C338.196 129.907 338.028 130.161 337.86 130.325C337.701 130.498 337.453 130.58 337.126 130.662H320.052L322.104 115.734C322.192 115.234 322.271 114.808 322.271 114.644C322.439 113.881 322.599 113.381 322.766 113.208C323.006 112.954 323.421 112.79 323.916 112.7C324.323 112.618 325.225 112.618 326.542 112.618Z"
      fill="white"
    />
    <path
      d="M356.16 100.735C356.982 101.916 357.557 102.679 357.804 103.179C358.211 103.688 358.459 104.196 358.538 104.532C358.706 104.869 358.706 105.205 358.706 105.631C358.706 105.795 358.706 106.222 358.626 106.894L357.15 117.606L355.338 130.671C355.01 133.624 354.763 135.65 354.683 136.659C354.683 137.085 354.603 137.504 354.603 137.93C354.603 139.103 354.763 139.948 355.098 140.628C355.585 141.465 356.655 141.973 358.211 142.31C359.361 142.482 361.739 142.564 365.355 142.564H383.49C386.611 142.564 388.663 142.482 389.645 142.392C391.864 142.228 393.34 141.719 394.162 140.874C394.897 140.038 395.471 138.767 395.799 136.995C396.046 135.904 396.373 133.796 396.788 130.671L399.247 112.545C399.742 108.83 399.989 106.386 399.989 105.295C399.989 103.605 399.654 102.506 398.92 101.916C398.345 101.325 397.275 100.989 395.799 100.907C394.737 100.735 392.518 100.735 389.15 100.735L386.859 117.606L385.047 130.671C383.985 130.671 383.243 130.589 382.916 130.589C382.588 130.417 382.341 130.335 382.173 130.171C382.093 129.917 382.093 129.581 382.093 129.154C382.093 128.818 382.173 128.309 382.261 127.473L384.312 112.455C384.64 109.929 384.799 108.24 384.887 107.313C384.967 106.64 384.967 106.05 384.967 105.541C384.967 104.024 384.799 103.015 384.312 102.424C383.818 101.58 382.748 101.071 381.192 100.907C380.122 100.735 377.831 100.735 374.295 100.735L372.004 117.015L370.192 130.671C369.043 130.671 368.308 130.589 367.981 130.589C367.654 130.417 367.406 130.253 367.319 129.917C367.319 129.663 367.239 129.326 367.239 128.9C367.319 128.654 367.406 128.227 367.406 127.637L369.458 112.545C370.033 108.83 370.28 106.386 370.28 105.205C370.28 103.605 369.945 102.506 369.21 101.916C368.716 101.416 367.814 101.071 366.664 100.907C365.842 100.735 364.693 100.735 363.136 100.735H356.16Z"
      fill="white"
    />
    <path
      d="M34.0677 90.4335C34.2795 91.2241 34.246 92.0615 33.9711 92.8398C33.6963 93.618 33.1927 94.3022 32.5238 94.8057C31.855 95.3092 31.051 95.6095 30.2136 95.6685C29.3761 95.7276 28.5429 95.5428 27.8191 95.1375C27.0953 94.7322 26.5136 94.1246 26.1475 93.3916C25.7813 92.6586 25.6472 91.833 25.7621 91.0193C25.877 90.2056 26.2357 89.4403 26.7929 88.8202C27.3501 88.2001 28.0808 87.753 28.8925 87.5355C29.981 87.2439 31.1377 87.3853 32.1082 87.9288C33.0788 88.4723 33.7836 89.3732 34.0677 90.4335ZM13.3148 81.4396C16.2327 77.5679 20.401 74.7671 25.1316 73.4996C29.8622 72.232 34.8723 72.5734 39.3352 74.4675C39.6782 74.6697 40.0622 74.7977 40.4618 74.8431C40.8613 74.8885 41.2673 74.8503 41.6528 74.7308C42.0382 74.6114 42.3944 74.4135 42.6977 74.1504C43.0009 73.8873 43.2443 73.5648 43.4117 73.2045C43.5791 72.8442 43.6667 72.4542 43.6687 72.0603C43.6706 71.6665 43.5869 71.2779 43.4231 70.9203C43.2593 70.5626 43.019 70.2441 42.7184 69.9858C42.4177 69.7276 42.0635 69.5355 41.6792 69.4223C36.0369 67.0288 29.7032 66.5977 23.7229 68.2001C17.7426 69.8025 12.473 73.3427 8.78343 78.2366C8.45994 78.8299 8.36469 79.5146 8.51515 80.1649C8.66562 80.8152 9.05171 81.3876 9.60257 81.777C10.1534 82.1663 10.8321 82.3466 11.514 82.2846C12.1959 82.2226 12.8353 81.9226 13.3148 81.4396ZM38.6225 82.8081C38.7758 82.4729 38.8604 82.1122 38.8713 81.7468C38.8822 81.3813 38.8193 81.0183 38.6861 80.6786C38.553 80.3388 38.3522 80.029 38.0953 79.7667C37.8384 79.5045 37.5304 79.2951 37.189 79.1505C33.8098 77.6788 29.9986 77.4014 26.4013 78.3653C22.8041 79.3291 19.6422 81.475 17.4516 84.4392C17.1145 85.0354 17.0104 85.7286 17.1587 86.3883C17.3069 87.0481 17.6975 87.6291 18.2568 88.0219C18.8161 88.4147 19.5056 88.5923 20.1957 88.5213C20.8858 88.4503 21.5289 88.1357 22.0041 87.6365C23.4415 85.7167 25.5019 84.3276 27.8422 83.7005C30.1825 83.0734 32.6615 83.2462 34.8662 84.1901C35.2072 84.3356 35.5751 84.4135 35.9488 84.4194C36.3225 84.4252 36.6947 84.3589 37.0441 84.2243C37.3935 84.0896 37.7131 83.8893 37.9848 83.6347C38.2564 83.3801 38.4747 83.0763 38.6271 82.7406L38.6225 82.8081Z"
      fill="white"
    />
    <path
      fill-rule="evenodd"
      clip-rule="evenodd"
      d="M65.1169 49.4493C71.3953 43.1709 80.7731 35.7487 87.136 30.9122C90.3735 31.2543 93.6596 30.5697 96.5145 28.9215C100.074 26.8662 102.672 23.4809 103.736 19.5104C104.8 15.5399 104.243 11.3094 102.188 7.74952C100.132 4.18965 96.747 1.59205 92.7764 0.528151C88.8059 -0.535745 84.5754 0.0212233 81.0155 2.07651C77.4557 4.1318 74.8581 7.51705 73.7942 11.4876C73.3368 13.1945 73.179 14.9495 73.311 16.6786C68.4799 23.0437 60.9528 32.5747 54.5975 38.9299C49.4881 44.0393 42.326 49.9062 36.4067 54.5206C35.899 54.3524 35.3852 54.1985 34.8657 54.0594C27.7703 52.1581 20.2102 53.1535 13.8486 56.8263C7.48705 60.4992 2.84504 66.5487 0.943826 73.6442C-0.95739 80.7396 0.0379186 88.2997 3.71078 94.6613C7.38365 101.023 13.4332 105.665 20.5287 107.566C27.6241 109.467 35.1842 108.472 41.5458 104.799C43.1399 103.879 44.6261 102.809 45.9883 101.611C50.8936 103.835 55.8991 106.276 59.9786 108.631C69.3453 114.039 81.5416 123.45 87.1961 127.933C87.1825 128.995 87.2451 130.058 87.3843 131.115C87.765 134.005 88.7113 136.792 90.1692 139.317C91.6265 141.842 93.567 144.055 95.8798 145.83C98.1926 147.605 100.832 148.906 103.648 149.661C106.465 150.416 109.402 150.608 112.292 150.227C115.183 149.847 117.97 148.901 120.495 147.443C123.02 145.985 125.233 144.044 127.008 141.731C128.783 139.418 130.085 136.778 130.839 133.962C131.594 131.146 131.786 128.209 131.406 125.319C131.025 122.428 130.079 119.641 128.621 117.117C127.164 114.592 125.223 112.379 122.91 110.604C120.598 108.829 117.958 107.527 115.142 106.773C112.326 106.018 109.388 105.826 106.498 106.206C103.607 106.587 100.82 107.533 98.295 108.991C97.9936 109.165 97.6967 109.346 97.4044 109.533C90.5639 106.819 76.6294 101.066 67.4169 95.7473C63.422 93.4409 58.9124 90.4063 54.6151 87.3363C56.2891 80.4297 55.2454 73.1334 51.6836 66.9642C51.4553 66.5688 51.2179 66.18 50.9715 65.7981C55.3266 60.2846 60.5273 54.0389 65.1169 49.4493ZM27.6397 103.038C39.9573 103.038 49.9426 93.0531 49.9426 80.7356C49.9426 68.418 39.9573 58.4327 27.6397 58.4327C15.3222 58.4327 5.33691 68.418 5.33691 80.7356C5.33691 93.0531 15.3222 103.038 27.6397 103.038Z"
      fill="white"
    />
    <path
      fill-rule="evenodd"
      clip-rule="evenodd"
      d="M85.4444 24.8547L87.7505 25.0985C89.767 25.3116 91.8136 24.8851 93.5914 23.8586L96.4303 28.7756L93.5914 23.8586C95.8086 22.5786 97.4264 20.4702 98.089 17.9973L103.492 19.445L98.089 17.9973C98.7516 15.5245 98.4047 12.8897 97.1247 10.6726C95.8446 8.45543 93.7362 6.83762 91.2634 6.17502C88.7905 5.51242 86.1557 5.8593 83.9386 7.13936C81.7215 8.4194 80.1037 10.5278 79.4411 13.0006C79.1562 14.0639 79.0579 15.1566 79.1401 16.2335L79.309 18.4457L77.9677 20.213C73.1297 26.5871 65.3943 36.4007 58.7313 43.0637C54.1534 47.6416 48.1097 52.7066 42.7399 56.9754C44.4598 58.0707 46.0528 59.3477 47.4918 60.7792C51.6584 55.5592 56.545 49.7536 60.9831 45.3155C67.567 38.7316 77.2248 31.1027 83.5983 26.258L85.4444 24.8547ZM51.2655 65.4264C51.1671 65.5507 51.0691 65.6746 50.9715 65.7981C51.2179 66.18 51.4553 66.5688 51.6836 66.9642C54.1225 71.1885 55.3808 75.9413 55.3943 80.7356C55.4005 82.9429 55.1429 85.159 54.6151 87.3363C54.7271 87.4164 54.8393 87.4964 54.9516 87.5763C59.1472 90.564 63.5261 93.501 67.4169 95.7473C76.6294 101.066 90.5639 106.819 97.4044 109.533C97.6967 109.346 97.9936 109.165 98.295 108.991C100.82 107.533 103.607 106.587 106.498 106.206C109.388 105.826 112.326 106.018 115.142 106.773L113.629 112.42C111.554 111.864 109.39 111.722 107.261 112.002C105.132 112.283 103.078 112.98 101.218 114.054C100.996 114.182 100.777 114.315 100.562 114.453L98.037 116.074L95.2483 114.967C88.4277 112.261 74.1221 106.369 64.4939 100.81C60.7895 98.6715 56.7377 95.9844 52.8576 93.2563C52.1646 94.6494 51.3595 95.977 50.4542 97.2272C54.7868 99.2353 59.1733 101.416 62.9016 103.568C72.6879 109.218 85.1934 118.884 90.8283 123.352L93.0784 125.137L93.0417 128.008C93.0317 128.79 93.0778 129.573 93.1803 130.352C93.4607 132.481 94.1578 134.534 95.2317 136.393L95.2323 136.395C96.3058 138.254 97.7352 139.884 99.4388 141.192C101.142 142.499 103.087 143.458 105.162 144.014C107.236 144.57 109.4 144.712 111.529 144.431L112.292 150.227C109.402 150.608 106.465 150.416 103.648 149.661C100.832 148.906 98.1926 147.605 95.8798 145.83C93.567 144.055 91.6265 141.842 90.1692 139.317C88.7113 136.792 87.765 134.005 87.3843 131.115C87.2451 130.058 87.1825 128.995 87.1961 127.933C81.5416 123.45 69.3453 114.039 59.9786 108.631C55.9975 106.332 51.1346 103.953 46.3434 101.772C46.225 101.719 46.1066 101.665 45.9883 101.611C44.6261 102.809 43.1399 103.879 41.5458 104.799C37.2819 107.261 32.4796 108.52 27.6397 108.51C25.2586 108.505 22.8684 108.193 20.5287 107.566C13.4332 105.665 7.38365 101.023 3.71078 94.6613C1.2457 90.3916 -0.0132566 85.5821 0.000105254 80.7356C0.00665199 78.3609 0.31863 75.9774 0.943826 73.6442C2.84504 66.5487 7.48705 60.4992 13.8486 56.8263C18.0788 54.3841 22.8388 53.1257 27.6397 53.1156C30.0589 53.1105 32.4884 53.4223 34.8657 54.0594C35.3852 54.1985 35.899 54.3524 36.4067 54.5206C36.5673 54.3954 36.7288 54.2693 36.8912 54.1423C42.7131 49.5884 49.6267 43.9007 54.5975 38.9299C60.9528 32.5747 68.4799 23.0437 73.311 16.6786C73.179 14.9495 73.3368 13.1945 73.7942 11.4876C74.8581 7.51705 77.4557 4.1318 81.0155 2.07651C84.5754 0.0212233 88.8059 -0.535745 92.7764 0.528151C96.747 1.59205 100.132 4.18965 102.188 7.74952C104.243 11.3094 104.8 15.5399 103.736 19.5104C102.672 23.4809 100.074 26.8662 96.5145 28.9215C93.6596 30.5697 90.3735 31.2543 87.136 30.9122C80.7731 35.7487 71.3953 43.1709 65.1169 49.4493C60.6302 53.936 55.5594 60.0055 51.2655 65.4264ZM44.9964 94.7426C46.206 93.2456 47.2259 91.5889 48.0195 89.8091C48.555 88.6082 48.9874 87.3512 49.3056 86.0494C49.7219 84.3464 49.9426 82.5668 49.9426 80.7356C49.9426 76.7066 48.8743 72.9272 47.0055 69.6651C46.8229 69.3465 46.6328 69.0328 46.4352 68.7243C45.6735 67.5349 44.802 66.4225 43.835 65.4015C42.0967 63.5662 40.0498 62.0262 37.7774 60.8646C36.801 60.3654 35.7829 59.9362 34.7299 59.5833C34.3205 59.4462 33.9059 59.3206 33.4864 59.2069C31.6231 58.7021 29.6629 58.4327 27.6397 58.4327C23.5807 58.4327 19.7748 59.5171 16.4961 61.4119C11.4215 64.3447 7.60974 69.219 6.07619 75.0194C5.59387 76.8437 5.33691 78.7596 5.33691 80.7356C5.33691 84.8328 6.44175 88.672 8.36975 91.9714C11.3107 97.0044 16.167 100.782 21.9389 102.303C23.7586 102.783 25.6693 103.038 27.6397 103.038C31.7068 103.038 35.5196 101.95 38.8027 100.048C40.0741 99.3115 41.2661 98.453 42.3626 97.4886C43.3163 96.6499 44.1977 95.731 44.9964 94.7426ZM112.292 150.227C115.183 149.847 117.97 148.901 120.495 147.443C123.02 145.985 125.233 144.044 127.008 141.731C128.783 139.418 130.085 136.778 130.839 133.962C131.594 131.146 131.786 128.209 131.406 125.319C131.025 122.428 130.079 119.641 128.621 117.117C127.164 114.592 125.223 112.379 122.91 110.604C120.598 108.829 117.958 107.527 115.142 106.773L113.629 112.42C115.703 112.976 117.648 113.935 119.351 115.242C121.055 116.549 122.484 118.18 123.558 120.039L123.558 120.04C124.632 121.9 125.329 123.953 125.61 126.082C125.89 128.211 125.748 130.375 125.193 132.449C124.637 134.524 123.678 136.468 122.37 138.172C121.063 139.876 119.432 141.306 117.572 142.38C115.712 143.454 113.659 144.151 111.529 144.431L112.292 150.227Z"
      fill="white"
    />
    <path
      d="M32.0409 146.111C29.9142 142.427 29.3378 138.05 30.4387 133.941C31.5396 129.832 34.2275 126.329 37.9111 124.203C41.5948 122.076 45.9724 121.5 50.081 122.601C54.1895 123.701 57.6925 126.389 59.8192 130.073C61.946 133.757 62.5223 138.134 61.4214 142.243C60.3205 146.351 57.6326 149.854 53.949 151.981C50.2653 154.108 45.8877 154.684 41.7792 153.583C37.6706 152.482 34.1676 149.794 32.0409 146.111Z"
      fill="white"
    />
    <path
      d="M9.45999 26.8049C7.92513 24.1464 7.5092 20.9871 8.30371 18.022C9.09821 15.0569 11.0381 12.5288 13.6965 10.994C16.355 9.45911 19.5143 9.04317 22.4794 9.83768C25.4445 10.6322 27.9726 12.572 29.5074 15.2305C31.0423 17.8889 31.4582 21.0482 30.6637 24.0134C29.8692 26.9785 27.9294 29.5066 25.2709 31.0414C22.6125 32.5763 19.4532 32.9922 16.488 32.1977C13.5229 31.4032 10.9949 29.4633 9.45999 26.8049Z"
      fill="white"
    />
  </svg>
);
const items = [
  {
    key: '1',
    label: '中文',
  },
  {
    key: '2',
    label: 'English',
  },
  {
    key: '3',
    label: 'Deutsch',
  },
  {
    key: '4',
    label: '日本語',
  },
  {
    key: '5',
    label: 'Indonesian',
  },
];
let instanceId = '675c4b15-8172-4f56-8223-92972646c387';
// 获取环境变量：系统环境
const { UMI_ENV } = process.env;
// 如果是生产环境
if (UMI_ENV === 'production') {
  instanceId = '214856f0-33ff-4f68-b461-cb3ccda953d2';
} else if (UMI_ENV == 'undefined') {
  instanceId = '675c4b15-8172-4f56-8223-92972646c387';
} else {
  // 其他环境
  instanceId = '675c4b15-8172-4f56-8223-92972646c387';
}
let offsetXRef = 0;
class LoginContent extends Component {
  passwordFormRef = React.createRef();

  constructor(props) {
    super(props);
    this.state = {
      captcha: '',
      loadings: [],
      langText: '中文',
      language: true,
      captchaStatus: true,
      ImageBg: [
        ImageBg1,
        ImageBg2,
        ImageBg3,
        ImageBg4,
        ImageBg5,
        ImageBg6,
        ImageBg7,
        ImageBg8,
        ImageBg9,
        ImageBg10,
      ],
    };
    this.formRef = React.createRef();
  }

  componentDidMount() {
    let lang = localStorage.getItem('lang');
    if (lang) {
      setLocale(lang, false);

      if (lang == 'zh-CN') {
        this.setState({
          langText: '中文',
          defaultSelectedKeys: '1',
        });
      } else if (lang == 'en-US') {
        this.setState({
          langText: 'English',
          defaultSelectedKeys: '2',
        });
      } else if (lang == 'de-DE') {
        this.setState({
          langText: 'Deutsch',
          defaultSelectedKeys: '3',
          languageValue: '3',
          initLanguageValue: '3',
        });
      } else if (lang == 'ja') {
        this.setState({
          langText: '日本語',
          defaultSelectedKeys: '4',
        });
      } else if (lang == 'id-ID') {
        this.setState({
          langText: 'Indonesian',
          defaultSelectedKeys: '5',
        });
      }
    } else {
      let newLang = navigator.language || navigator.userLanguage; //常规浏览器语言和IE浏览器
      setLocale(newLang, false);

      if (newLang == 'zh-CN') {
        this.setState({
          langText: '中文',
          defaultSelectedKeys: '1',
        });
      } else if (newLang == 'en-US') {
        this.setState({
          langText: 'English',
          defaultSelectedKeys: '2',
        });
      } else if (newLang == 'de-DE') {
        this.setState({
          langText: 'Deutsch',
          defaultSelectedKeys: '3',
          languageValue: '3',
          initLanguageValue: '3',
        });
      } else if (newLang == 'ja') {
        this.setState({
          langText: '日本語',
          defaultSelectedKeys: '4',
        });
      } else if (newLang == 'id-ID') {
        this.setState({
          langText: 'Indonesian',
          defaultSelectedKeys: '5',
        });
      }
    }
    //暂时注释
    // let reloadPage = localStorage.getItem('reloadPage');
    // if (!reloadPage) {
    //   location.reload();
    //   localStorage.setItem('reloadPage', true);
    // }

    // 从cookie中获取信息
    this.getCookie();
  }
  componentWillUnmount() {
    // localStorage.removeItem('reloadPage');
  }
  componentDidUpdate(prevProps, prevState) {
    //存储语言大全
    this.languageLocal(getLocale());
  }
  //存储语言大全
  languageLocal = lang => {
    // let arr1 = [
    //   'zh-CN',
    //   'ko',
    //   'en-US',
    //   'zh',
    //   'sq',
    //   'ak',
    //   'zu',
    //   'zh-HK',
    //   'zh-TW',
    //   'ckb',
    //   'jv',
    //   'vi',
    //   'yo',
    //   'en-GB',
    //   'en-GB-oxendict',
    //   'en-IN',
    //   'en-NZ',
    //   'en-ZA',
    //   'en-AU',
    //   'en-CA',
    //   'en-IE',
    //   'en',
    //   'id',
    //   'hi',
    //   'yi',
    //   'it-IT',
    //   'it-CH',
    //   'it',
    //   'ilo',
    //   'ig',
    //   'hy',
    //   'su',
    //   'hu',
    //   'sd',
    //   'haw',
    //   'el',
    //   'he',
    //   'fy',
    //   'es-CL',
    //   'es-ES',
    //   'es-UY',
    //   'es-VE',
    //   'es-MX',
    //   'es-PE',
    //   'es-US',
    //   'es-419',
    //   'es-HN',
    //   'es-CR',
    //   'es-CO',
    //   'es-AR',
    //   'es',
    //   'uz',
    //   'uk',
    //   'ur',
    //   'wo',
    //   'ug',
    //   'cy',
    //   'wa',
    //   'tk',
    //   'tr',
    //   'ti',
    //   'to',
    //   'th',
    //   'ta',
    //   'te',
    //   'tg',
    //   'so',
    //   'ceb',
    //   'gd',
    //   'sw',
    //   'sl',
    //   'sk',
    //   'eo',
    //   'sn',
    //   'si',
    //   'sr',
    //   'sh',
    //   'sm',
    //   'sv',
    //   'ja',
    //   'chr',
    //   'tw',
    //   'ny',
    //   'ps',
    //   'pt-PT',
    //   'pt-BR',
    //   'pt',
    //   'pa',
    //   'no',
    //   'nn',
    //   'ne',
    //   'st',
    //   'hmn',
    //   'af',
    //   'my',
    //   'lus',
    //   'bn',
    //   'mi',
    //   'mni-Mtei',
    //   'mn',
    //   'mai',
    //   'mk',
    //   'ms',
    //   'ml',
    //   'mg',
    //   'mr',
    //   'mt',
    //   'rm',
    //   'mo',
    //   'ro',
    //   'rw',
    //   'lb',
    //   'lg',
    //   'ln',
    //   'lt',
    //   'lo',
    //   'lv',
    //   'la',
    //   'ku',
    //   'kok',
    //   'qu',
    //   'hr',
    //   'co',
    //   'xh',
    //   'ky',
    //   'kn',
    //   'cs',
    //   'ca',
    //   'gl',
    //   'nl',
    //   'ha',
    //   'ht',
    //   'kk',
    //   'ia',
    //   'gn',
    //   'gu',
    //   'ka',
    //   'km',
    //   'fi',
    //   'fil',
    //   'sa',
    //   'fr-CH',
    //   'fr-CA',
    //   'fr-FR',
    //   'fr',
    //   'fo',
    //   'ru',
    //   'doi',
    //   'dv',
    //   'de-CH',
    //   'de-LI',
    //   'de-DE',
    //   'de-AT',
    //   'de',
    //   'da',
    //   'tt',
    //   'ts',
    //   'tn',
    //   'br',
    //   'bho',
    //   'fa',
    //   'bs',
    //   'pl',
    //   'is',
    //   'nso',
    //   'bg',
    //   'bm',
    //   'be',
    //   'eu',
    //   'om',
    //   'or',
    //   'oc',
    //   'et',
    //   'ga',
    //   'ay',
    //   'ee',
    //   'ast',
    //   'az',
    //   'as',
    //   'am',
    //   'an',
    //   'ar',
    //   'nb',
    // ];

    // let translatedArr = [
    //   '中文-简体 (Chinese Simplified)',
    //   '한국어 (Korean)',
    //   'English (United States)',
    //   '中文 (Chinese)',
    //   'shqip (Albanian)',
    //   'Akan',
    //   'isiZulu (Zulu)',
    //   '中文-香港 (Chinese - Hong Kong)',
    //   '中文-繁體 (Chinese Traditional)',
    //   'کوردیی ناوەندی (Kurdish)',
    //   'Jawa (Javanese)',
    //   'Tiếng Việt (Vietnamese)',
    //   'Èdè Yorùbá (Yoruba)',
    //   'English (United Kingdom)',
    //   'English (United Kingdom, Oxford English Dictionary spelling)',
    //   'English (India)',
    //   'English (New Zealand)',
    //   'English (South Africa)',
    //   'English (Australia)',
    //   'English (Canada)',
    //   'English (Ireland)',
    //   'English',
    //   'Indonesia',
    //   'हिन्दी (Hindi)',
    //   'ייִדיש (Yiddish)',
    //   'italiano-Italia (Italian - Italy)',
    //   'italiano-Svizzera (Italian - Switzerland)',
    //   'italiano-Italian',
    //   'Ilokano',
    //   'Igbo',
    //   'հայերեն (Armenian)',
    //   'Basa Sunda (Sundanese)',
    //   'magyar (Hungarian)',
    //   'سنڌي (Sindhi)',
    //   'ʻŌlelo Hawaiʻi (Hawaiian)',
    //   'Ελληνικά (Greek)',
    //   'עברית (Hebrew)',
    //   'Frysk (Frisian)',
    //   'español-Chile (Spanish - Chile)',
    //   'español-España (Spanish - Spain)',
    //   'español-Uruguay (Spanish - Uruguay)',
    //   'español-Venezuel (Spanish - Venezuela)',
    //   'español-México (Spanish - Mexico)',
    //   'español-Perú (Spanish - Peru)',
    //   'español-Estados Unidos (Spanish - United States)',
    //   'español-Latinoamérica (Spanish - Latin America)',
    //   'español-Honduras (Spanish - Honduras)',
    //   'español-Costa Rica (Spanish - Costa Rica)',
    //   'español-Colombia (Spanish - Colombia)',
    //   'español-Argentina (Spanish - Argentina)',
    //   'español-Spanish',
    //   'o‘zbek (Uzbek)',
    //   'українська (Ukrainian)',
    //   'اردو (Urdu)',
    //   'Wolof',
    //   'ئۇيغۇرچە (Uyghur)',
    //   'Cymraeg (Welsh)',
    //   'wa',
    //   'türkmen dili (Turkmen)',
    //   'Türkçe (Turkish)',
    //   'ትግርኛ (Tigrinya)',
    //   'lea fakatonga (Tongan)',
    //   'ไทย (Thai)',
    //   'தமிழ் (Tamil)',
    //   'తెలుగు (Telugu)',
    //   'тоҷикӣ (Tajik)',
    //   'Soomaali (Somali)',
    //   'Cebuano',
    //   'Gàidhlig (Scottish Gaelic)',
    //   'Kiswahili (Swahili)',
    //   'slovenščina (Slovenian)',
    //   'slovenčina (Slovak)',
    //   'esperanto',
    //   'chiShona (Shona)',
    //   'සිංහල (Sinhala)',
    //   'српски (Serbian)',
    //   'srpskohrvatski',
    //   '萨摩亚语 (Samoan)',
    //   'svenska (Swedish)',
    //   '日本語 (Japanese)',
    //   'ᏣᎳᎩ (Cherokee)',
    //   '契维语 (Cheyenne)',
    //   '齐切瓦语 (Chichewa)',
    //   'پښتو (Pashto)',
    //   'português-Portugal (Portuguese - Portugal)',
    //   'português-Brasil (Portuguese - Brazil)',
    //   'português-Portuguese',
    //   'ਪੰਜਾਬੀ (Punjabi)',
    //   'norsk (Norwegian)',
    //   'norsk nynorsk (Norwegian Nynorsk)',
    //   'नेपाली (Nepali)',
    //   '南索托语 (Southern Sotho)',
    //   '苗语 (Hmong)',
    //   'Afrikaans',
    //   'မြန်မာ (Burmese)',
    //   'Mizo tawng',
    //   'বাংলা (Bengali)',
    //   'Māori',
    //   'mni (Mtei)',
    //   'монгол (Mongolian)',
    //   'मैथिली (Maithili)',
    //   'македонски (Macedonian)',
    //   'Melayu (Malay)',
    //   'മലയാളം (Malayalam)',
    //   'Malagasy',
    //   'मराठी (Marathi)',
    //   'Malti (Maltese)',
    //   'rumantsch (Romansh)',
    //   'română-Republica Moldova (Romanian - Republic of Moldova)',
    //   'română-Romanian',
    //   'Kinyarwanda',
    //   'Lëtzebuergesch (Luxembourgish)',
    //   'Luganda',
    //   'lingála (Lingala)',
    //   'lietuvių (Lithuanian)',
    //   'ລາວ (Lao)',
    //   'latviešu (Latvian)',
    //   '拉丁语 (Latin)',
    //   'Kurdî (Kurdish)',
    //   'कोंकणी (Konkani)',
    //   'Runasimi',
    //   'hrvatski (Croatian)',
    //   '科西嘉语 (Corsican)',
    //   'IsiXhosa (Xhosa)',
    //   'кыргызча (Kyrgyz)',
    //   'ಕನ್ನಡ (Kannada)',
    //   'čeština (Czech)',
    //   'català (Catalan)',
    //   'galego (Galician)',
    //   'Nederlands (Dutch)',
    //   'Hausa',
    //   'créole haïtien (Haitian Creole)',
    //   'қазақ тілі (Kazakh)',
    //   'interlingua',
    //   '瓜拉尼语 (Guarani)',
    //   'ગુજરાતી (Gujarati)',
    //   'ქართული (Georgian)',
    //   'ខ្មែរ (Khmer)',
    //   'suomi (Finnish)',
    //   'Filipino',
    //   'संस्कृत भाषा (Sanskrit)',
    //   'français-Suisse (French - Switzerland)',
    //   'français-Canada (French - Canada)',
    //   'français-France (French - France)',
    //   'français-French',
    //   'føroyskt-Faroese',
    //   'русский (Russian)',
    //   'डोगरी (Dogri)',
    //   'ދިވެހި(Divehi)',
    //   'Deutsch-Schweiz (German - Switzerland)',
    //   'Deutsch-Liechtenstein',
    //   'Deutsch-Deutschland (German - Germany)',
    //   'Deutsch-Österreich (German - Austria)',
    //   'Deutsch-German)',
    //   'dansk (Danish)',
    //   'татар (Tatar)',
    //   'Xitsonga',
    //   '茨瓦纳语 (Tswana)',
    //   'brezhoneg (Breton)',
    //   'भोजपुरी (Bhojpuri)',
    //   'فارسی(Persian)',
    //   'bosanski (Bosnian)',
    //   'polski (Polish)',
    //   'íslenska (Icelandic)',
    //   '北索托语 (Northern Sotho)',
    //   'български (Bulgarian)',
    //   'bamanakan',
    //   'беларуская (Belarusian)',
    //   'euskara (Basque)',
    //   'Oromoo',
    //   'ଓଡ଼ିଆ (Odia)',
    //   '奥克语 (Occitan)',
    //   'eesti (Estonian)',
    //   'Gaeilge (Irish)',
    //   'Aymar',
    //   'Eʋegbe (Ewe)',
    //   'asturianu (Asturian)',
    //   'azərbaycan (Azerbaijani)',
    //   'অসমীয়া (Assamese)',
    //   'አማርኛ (Amharic)',
    //   'aragonés (Aragonese)',
    //   'العربية (Arabic)',
    //   'norsk bokmål (Norwegian Bokmål)',
    // ];

    // let list = translatedArr.map((item, index) => {
    //   return {
    //     value: arr1[index],
    //     label: item,
    //   };
    // });
    // let sortList = list.sort((a, b) =>
    //   a.value.toLowerCase().localeCompare(b.value.toLowerCase()),
    // );

    // localStorage.setItem('languageLocal', JSON.stringify(sortList));

    if (lang === 'zh-CN' || getLocale() == 'zh-CN') {
      localStorage.setItem('languageLocal', JSON.stringify(LanguageZhCN));
    } else if (lang === 'en-US' || getLocale() == 'en-US') {
      localStorage.setItem('languageLocal', JSON.stringify(LanguageEnUS));
    } else if (lang === 'de-DE' || getLocale() == 'de-DE') {
      localStorage.setItem('languageLocal', JSON.stringify(LanguageDeDE));
    } else if (lang === 'ja' || getLocale() == 'ja') {
      localStorage.setItem('languageLocal', JSON.stringify(LanguageJa));
    }
  };
  //切换语言
  handleMenuClick = e => {
    const langConfig = {
      '1': {
        langText: '中文',
        locale: 'zh-CN',
      },
      '2': {
        langText: 'English',
        locale: 'en-US',
      },
      '3': {
        langText: 'Deutsch',
        locale: 'de-DE',
      },
      '4': {
        langText: '日本語',
        locale: 'ja',
      },
      '5': {
        langText: 'Indonesian',
        locale: 'id-ID',
      },
    };

    if (langConfig[e.key]) {
      const { langText, locale } = langConfig[e.key];
      this.setState({ langText });
      setLocale(locale, false);
      localStorage.setItem('lang', locale);
      // 调用 global/changeLang action 同步全局状态
      console.log('locale', locale);
      this.props.dispatch({
        type: 'global/changeLang',
        payload: locale,
      });
    }
  };
  /**
   * 验证通过 进行登录
   * @param values
   */
  onFinish = values => {
    let { captcha } = this.state;
    if (captcha) {
      // 设置 登录按钮的loading状态
      this.setState(({ loadings }) => {
        const newLoadings = [...loadings];
        newLoadings[0] = true;
        return {
          loadings: newLoadings,
        };
      });
      // console.log('Success:', values);
      let { remember, userEmail, userPassword } = values;
      // 对密码进行加密
      values.userPassword = crypto.aesEncrypt(userPassword);
      // 去掉邮件前后的空格
      values.userEmail = userEmail.trim();
      this.props.dispatch({
        type: 'login/login',
        payload: values,
        callback: response => {
          let { code, data, msg } = response;
          if (code === 200) {
            localStorage.setItem('Authorization', `Bearer ${data.accessToken}`);
            notification.success({
              message: getIntl().formatMessage({
                id: 'login.success',
              }),
            });
            // 记住密码  通过cookie实现
            if (remember) {
              // cookie 3天有效期
              let expires = new Date();
              expires.setDate(expires.getDate() + 3);

              cookie.save('userEmail', userEmail, { expires });
              cookie.save('userPassword', values.userPassword, { expires });
              cookie.save('remember', remember, { expires });
            } else {
              cookie.remove('userEmail');
              cookie.remove('userPassword');
              cookie.remove('remember');
            }
            // 取消 登录按钮的loading状态
            this.setState(({ loadings }) => {
              const newLoadings = [...loadings];
              newLoadings[0] = false;
              return {
                loadings: newLoadings,
              };
            });
            //重置historyConnect，重新记录ccp已登录实例
            localStorage.setItem('historyConnect', '');

            // 跳转链接  修复livechat进入首页依然存在问题，强刷新
            //动态根目录，存储roleId
            this.props.dispatch({
              type: 'layouts/getOrSetUser',
              callback: response => {
                if (response.code == 200) {
                  let roleList = response.data.roleList;
                  let roleId = roleList?.[0]?.roleId;
                  if (['1002', '1004'].includes(roleId)) {
                    // window.location.href = '/#/campaignCalendar';
                    history.push('/campaignCalendar');
                    window.location.reload();
                  } else {
                    window.location.href = '/';
                  }
                  // 读取浏览器的语言
                  const browserLanguage =
                    navigator.language || navigator.userLanguage || 'en-US';
                  console.log('当前浏览器语言：', browserLanguage);
                  sessionStorage.setItem('roleId', roleId);
                  if (localStorage.getItem('worktable_setting_ai') === null) {
                    localStorage.setItem('worktable_setting_ai', 'true');
                  }
                  if (
                    localStorage.getItem('worktable_setting_translation') ===
                    null
                  ) {
                    localStorage.setItem(
                      'worktable_setting_translation',
                      'false',
                    );
                  }
                  if (
                    !localStorage.getItem('worktable_setting_translation_code')
                  ) {
                    localStorage.setItem(
                      'worktable_setting_translation_code',
                      browserLanguage,
                    );
                  }
                  // window.location.href = '/';
                } else {
                  notification.error({
                    message: response.msg,
                  });
                }
              },
            });
            // history.replace('/');
          } else {
            notification.error({
              msg,
            });
            cookie.remove('userEmail');
            cookie.remove('userPassword');
            cookie.remove('remember');
          }

          // 取消 登录按钮的loading状态
          this.setState(({ loadings }) => {
            const newLoadings = [...loadings];
            newLoadings[0] = false;
            return {
              loadings: newLoadings,
            };
          });
        },
      });
    } else {
      notification.warning({
        message: getIntl().formatMessage({
          id: 'login.user.captcha.input1',
          defaultValue: '请拖动滑块验证',
        }),
      });
    }
  };
  onFinishFailed = errorInfo => {
    // console.log('Failed:', errorInfo);
  };
  handleClick = captcha => {
    // console.log('captcha:', captcha);
    this.setState({
      captcha,
    });
  };
  changeLanguage = () => {
    let language = getLocale();
    if (language == 'en-US') {
      setLocale('zh-CN', true);
    } else {
      setLocale('en-US', true);
    }
  };
  /**
   * 跳转宣传页
   */
  goHomePage = () => {
    history.push('/home');
  };
  /**
   * 获取cookie
   */
  getCookie = () => {
    let userEmail = cookie.load('userEmail');
    let userPassword = cookie.load('userPassword');
    let remember = cookie.load('remember');

    // 如果有信息 显示到页面
    if (userEmail && userPassword) {
      // 解密放在输入框
      userPassword = encrypt.aesDecrypt(userPassword);
      this.formRef.current.setFieldsValue({
        userEmail,
        userPassword,
        remember,
      });
    }
  };
  // 获取随机数
  getRandomInt = (min, max) => {
    min = Math.ceil(min);
    max = Math.floor(max);
    return Math.floor(Math.random() * (max - min + 1)) + min;
  };
  // 获取滑动验证背景图
  getCaptcha = async () => {
    let { ImageBg } = this.state;
    const randomNum = this.getRandomInt(0, 9);
    try {
      const res = await createPuzzle(ImageBg[randomNum], { format: 'blob' });
      offsetXRef = res.x;
      return {
        bgUrl: res.bgUrl,
        puzzleUrl: res.puzzleUrl,
      };
    } catch (error) {
      notification.error({
        message: error,
      });
    }
  };

  render() {
    let { loadings, defaultSelectedKeys, langText, captchaStatus } = this.state;
    let language = getLocale();

    return (
      <>
        <CookieConsent />
        <div className={styles.loginContent}>
          <div className={styles.goHome} onClick={this.goHomePage}>
            <img src={LoginReturn} />
            <span>
              <FormattedMessage
                id="home.page.menu.home"
                defaultMessage="首页"
              />
            </span>
          </div>
          <div className={styles.languageIcon}>
            <Dropdown
              menu={{
                items,
                selectable: true,
                defaultSelectedKeys: [defaultSelectedKeys],
                onClick: this.handleMenuClick,
              }}
              overlayClassName="langDownPersonal"
            >
              <div className={styles.langContent}>
                <img src={LangIcon} />
                <span>{langText}</span>
              </div>
            </Dropdown>
          </div>
          {/* <img
            src={language === 'zh-CN' ? CnIcon : EnIcon}
            onClick={this.changeLanguage}
          /> */}
          <div className={styles.loginDetailContent}>
            <div className={styles.leftLoginContent}>
              <img src={language === 'zh-CN' ? leftImg : leftImgEn}></img>
            </div>
            <div className={styles.rightLoginContent}>
              <div className={styles.logoContent} onClick={this.goHomePage}>
                <img className={styles.logoImg} src={LogoImg} />
                {/* <div className={styles.logoLine}></div>
                <div className={styles.platformName}>ConnectNow</div> */}
              </div>
              <div className={styles.formContent}>
                <div className={styles.formContentTitle}>
                  <FormattedMessage id="login.title" />
                </div>
                {/* <div className={styles.formContentTitle}>
                  <FormattedMessage id="login.title" />
                  <span>ConnectNow</span>
                </div> */}
                <Form
                  name="basic"
                  labelCol={{
                    span: 0,
                  }}
                  wrapperCol={{
                    span: 20,
                  }}
                  style={{
                    maxWidth: 600,
                    marginTop: 20,
                  }}
                  layout="vertical"
                  // initialValues={{
                  //   remember: true,
                  // }}
                  onFinish={this.onFinish}
                  onFinishFailed={this.onFinishFailed}
                  autoComplete="off"
                  ref={this.formRef}
                >
                  <Form.Item
                    label={getIntl().formatMessage({
                      id: 'login.email',
                    })}
                    name="userEmail"
                    rules={[
                      {
                        required: true,
                        message: (
                          <FormattedMessage id="login.user.email.input" />
                        ),
                      },
                      {
                        min: 1,
                        max: 40,
                        // type: 'email',
                        pattern:
                          "^\\s*\\w+([-+.']\\w+)*@\\w+([-.]\\w+)*\\.\\w+([-.]\\w+)*\\s*$",
                        message: (
                          <FormattedMessage id="login.user.email.pattern" />
                        ),
                      },
                    ]}
                  >
                    <Input
                      placeholder={getIntl().formatMessage({
                        id: 'login.user.email.input',
                      })}
                      prefix={<UserOutlined />}
                    />
                  </Form.Item>

                  <Form.Item
                    label={getIntl().formatMessage({
                      id: 'login.pwd',
                    })}
                    name="userPassword"
                    rules={[
                      {
                        required: true,
                        message: (
                          <FormattedMessage id="login.user.password.input" />
                        ),
                      },
                    ]}
                  >
                    <Input
                      type="password"
                      placeholder={getIntl().formatMessage({
                        id: 'login.user.password.input',
                      })}
                      prefix={<LockOutlined />}
                      ref={this.passwordFormRef}
                    />
                  </Form.Item>
                  <Form.Item className={styles.noMarginBtm}>
                    <Row gutter={24}>
                      <Col span={24}>
                        <Form.Item
                          label={getIntl().formatMessage({
                            id: 'register.captcha',
                          })}
                          name="captcha"
                          rules={[
                            {
                              required: false,
                              message: (
                                <FormattedMessage id="login.user.captcha.input1" />
                              ),
                            },
                          ]}
                        >
                          <Row gutter={24}>
                            <Col span={24}>
                              <SliderCaptch
                                mode="float"
                                style={{ zIndex: 99999 }}
                                request={this.getCaptcha}
                                onVerify={async data => {
                                  if (
                                    data.x >= offsetXRef - 5 &&
                                    data.x < offsetXRef + 5
                                  ) {
                                    this.passwordFormRef.current.focus();
                                    this.setState({
                                      captchaStatus: false,
                                      captcha: '1',
                                    });
                                    return Promise.resolve();
                                  }
                                  return Promise.reject();
                                }}
                                tipIcon={{
                                  default: <LoginCodeIcon />,
                                }}
                                tipText={{
                                  default: getIntl().formatMessage({
                                    id: 'register.captcha.operation.tips',
                                    defaultValue: '向右拖动滑块填充拼图',
                                  }),
                                }}
                                bgSize={{
                                  width: 320,
                                }}
                                loadingDelay={300}
                              />
                            </Col>
                          </Row>
                        </Form.Item>
                      </Col>
                    </Row>
                  </Form.Item>

                  <Form.Item
                    name="remember"
                    valuePropName="checked"
                    wrapperCol={{
                      offset: 0,
                      span: 16,
                    }}
                  >
                    <Checkbox>
                      <FormattedMessage id="login.remember" />
                    </Checkbox>
                  </Form.Item>

                  <Form.Item>
                    <Button
                      className={styles.normalLogin}
                      type="primary"
                      htmlType="submit"
                      loading={loadings[0]}
                    >
                      <FormattedMessage id="login.button" />
                    </Button>
                  </Form.Item>
                  {/*<Form.Item>
                    <Button
                      className={styles.customerServicePersonnelLogin}
                      type="primary"
                      htmlType="submit"
                    >
                      客服人员登录
                    </Button>
                  </Form.Item>*/}
                  <Form.Item className={styles.normalLoginBottom}>
                    <FormattedMessage id="register.no.account" />
                    {/* <Link to={'/register'}>
                      <FormattedMessage id="register.now.register" />
                    </Link> */}
                    <Link to={'/site'}>
                      <FormattedMessage id="register.now.register" />
                    </Link>
                  </Form.Item>
                </Form>
              </div>
            </div>
          </div>

          <ChatWidget instanceId={instanceId}></ChatWidget>
        </div>
      </>
    );
  }
}

const mapStateToProps = ({ login }) => {
  return {
    ...login,
  };
};
export default connect(mapStateToProps)(LoginContent);
